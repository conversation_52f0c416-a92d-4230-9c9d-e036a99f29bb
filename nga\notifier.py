"""
Bark 推送通知模块
"""
import logging
from typing import Optional

import httpx

from config import BarkConfig


class BarkNotifier:
    """Bark 推送通知器"""
    
    def __init__(self, config: BarkConfig):
        """
        初始化 Bark 通知器
        
        Args:
            config: Bark 配置对象
        """
        self.config = config
        self.client = httpx.Client(timeout=10.0) if config.enabled else None
        
    def __del__(self):
        """清理资源"""
        if self.client:
            self.client.close()
    
    def send_notification(
        self, 
        title: str, 
        body: str, 
        priority: Optional[int] = None,
        **kwargs
    ) -> bool:
        """
        发送 Bark 推送通知
        
        Args:
            title: 通知标题
            body: 通知内容
            priority: 优先级 (0-10)，默认使用配置中的值
            **kwargs: 其他 Bark 参数
            
        Returns:
            bool: 推送是否成功
        """
        if not self.config.enabled or not self.client:
            return False
            
        if not self.config.server_url or not self.config.device_key:
            logging.warning("Bark 配置不完整，跳过推送")
            return False
            
        try:
            url = f"{self.config.server_url.rstrip('/')}/{self.config.device_key}"
            
            payload = {
                "title": title[:100],  # 限制标题长度
                "body": body[:1000],   # 限制内容长度
                "priority": priority or self.config.default_priority,
                **kwargs
            }
            
            response = self.client.post(url, json=payload)
            
            if response.status_code == 200:
                logging.debug(f"Bark 推送成功: {title}")
                return True
            else:
                logging.warning(f"Bark 推送失败，状态码: {response.status_code}")
                return False
                
        except Exception as e:
            logging.error(f"Bark 推送异常: {e}")
            return False
    
    def send_nga_reply_notification(
        self, 
        author: str, 
        content: str, 
        postdate: str,
        tid: Optional[int] = None
    ) -> bool:
        """
        发送 NGA 回复通知
        
        Args:
            author: 回复作者
            content: 回复内容
            postdate: 发布时间
            tid: 主题ID
            
        Returns:
            bool: 推送是否成功
        """
        title = f"NGA新回复 - {author}"
        
        # 格式化推送内容
        body_parts = [
            f"时间: {postdate}",
            f"内容: {content[:200]}"  # 限制内容长度
        ]
        
        if tid:
            body_parts.append(f"主题ID: {tid}")
            
        body = "\n".join(body_parts)
        
        return self.send_notification(
            title=title,
            body=body,
            priority=1  # 新回复使用中等优先级
        )
    
    def send_error_notification(self, error_msg: str) -> bool:
        """
        发送错误通知
        
        Args:
            error_msg: 错误信息
            
        Returns:
            bool: 推送是否成功
        """
        return self.send_notification(
            title="NGA爬虫错误",
            body=error_msg,
            priority=2  # 错误使用高优先级
        ) 