package config

import (
	"strings"

	"github.com/spf13/viper"
)

// Config 应用配置结构
type Config struct {
	Database DatabaseConfig `mapstructure:"database"`
	NGA      NGAConfig      `mapstructure:"nga"`
	Bark     BarkConfig     `mapstructure:"bark"`
	Log      LogConfig      `mapstructure:"log"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	URL  string `mapstructure:"url"`
	Echo bool   `mapstructure:"echo"`
}

// NGAConfig NGA API 配置
type NGAConfig struct {
	PrefixURL string            `mapstructure:"prefix_url"`
	Headers   map[string]string `mapstructure:"headers"`
	Cookie    string            `mapstructure:"cookie"`
}

// BarkConfig Bark 推送配置
type BarkConfig struct {
	Enabled         bool   `mapstructure:"enabled"`
	ServerURL       string `mapstructure:"server_url"`
	DeviceKey       string `mapstructure:"device_key"`
	DefaultPriority int    `mapstructure:"default_priority"`
}

// LogConfig 日志配置
type LogConfig struct {
	Level    string `mapstructure:"level"`
	FilePath string `mapstructure:"file_path"`
	Format   string `mapstructure:"format"`
}

// AppConfig 全局配置实例
var AppConfig *Config

// Init 初始化配置
func Init() error {
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	viper.AddConfigPath(".")
	viper.AddConfigPath("./configs")

	// 设置环境变量前缀
	viper.SetEnvPrefix("NGA")
	viper.AutomaticEnv()
	viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))

	// 设置默认值
	setDefaults()

	// 读取配置文件（如果存在）
	if err := viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
			return err
		}
	}

	// 解析配置到结构体
	AppConfig = &Config{}
	return viper.Unmarshal(AppConfig)
}

// setDefaults 设置默认配置值
func setDefaults() {
	// 数据库默认配置
	viper.SetDefault("database.url", "postgresql://nga:password@localhost:5432/nga?sslmode=disable")
	viper.SetDefault("database.echo", false)

	// NGA API 默认配置
	viper.SetDefault("nga.prefix_url", "http://ngabbs.com/app_api.php?")
	viper.SetDefault("nga.headers.User-Agent", "NGA_skull/6.0.7(iPhone11,6;iOS 12.2)")
	viper.SetDefault("nga.headers.X-User-Agent", "NGA_skull/6.0.7(iPhone11,6;iOS 12.2)")
	viper.SetDefault("nga.headers.Content-Type", "application/x-www-form-urlencoded")
	viper.SetDefault("nga.cookie", "")

	// Bark 推送默认配置
	viper.SetDefault("bark.enabled", false)
	viper.SetDefault("bark.server_url", "https://api.day.app")
	viper.SetDefault("bark.device_key", "")
	viper.SetDefault("bark.default_priority", 0)

	// 日志默认配置
	viper.SetDefault("log.level", "info")
	viper.SetDefault("log.file_path", "./logs/nga.log")
	viper.SetDefault("log.format", "json")
}
