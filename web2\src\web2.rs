use actix_web::web::Json;
use actix_web::{get, web, App, HttpServer, Responder};
use entity::users;
use mycore::config::db::init_db;
use mycore::config::log::init_log;
use mycore::config::rt::RT;
use mycore::config::state::AppState;
use sea_orm::entity::EntityTrait;
use serde_json::json;
use std::sync::Arc;
use std::time::Duration;
use tokio::time::sleep;

#[get("/hello/{name}")]
async fn greet(name: web::Path<String>, data: web::Data<Arc<AppState>>) -> impl Responder {
    let db = &data.db;
    let user = users::Entity::find_by_id(1).one(db).await.unwrap();
    sleep(Duration::from_secs(3)).await;
    Json(json!({"a": name.to_string(), "b": user}))
}

pub async fn start_web(addr: &str) {
    init_log();
    let state = init_db();
    RT.block_on(async {
        HttpServer::new(move || {
            App::new().service(greet)
                .app_data(web::Data::new(state.clone()))
        })
            .workers(1)
            .bind(addr)?
            .run()
            .await
    }).unwrap();
}