{"cells": [{"metadata": {"ExecuteTime": {"end_time": "2025-05-06T01:07:48.481022Z", "start_time": "2025-05-06T01:07:48.464108Z"}}, "cell_type": "code", "source": "val  a = 1", "id": "cc525c1684aec4c1", "outputs": [{"ename": "SyntaxError", "evalue": "invalid syntax (*********.py, line 1)", "output_type": "error", "traceback": ["  \u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[5]\u001b[39m\u001b[32m, line 1\u001b[39m\n\u001b[31m    \u001b[39m\u001b[31mval  a = 1\u001b[39m\n         ^\n\u001b[31mSyntaxError\u001b[39m\u001b[31m:\u001b[39m invalid syntax\n"]}], "execution_count": 5}], "metadata": {}, "nbformat": 4, "nbformat_minor": 5}