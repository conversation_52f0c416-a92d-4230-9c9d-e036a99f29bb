use crate::service::user::{AuthR<PERSON>ponse, Clai<PERSON>};
use anyhow::Result;
use axum::body::Body;
use axum::extract::{Request, State};
use axum::http::StatusCode;
use axum::middleware::Next;
use axum::response::{IntoResponse, Response};
use axum::Json;
use axum_session::{Session, SessionAnyPool, SessionConfig, SessionLayer, SessionMode, SessionStore};
use axum_session_redispool::SessionRedisPool;
use axum_session_sqlx::{SessionMySqlPool, SessionPgPool, SessionSqlitePool};
use entity::users;
use jsonwebtoken::{Algorithm, DecodingKey, TokenData, Validation};
use log::{error, info};
use mycore::config::state::AppState;
use redis::{Commands, JsonCommands};
use sea_orm::{ConnectionTrait, Database, DatabaseBackend, EntityTrait};
use serde::{Deserialize, Serialize};
use sqlx::types::uuid;
use std::env;
use std::sync::{Arc, LazyLock};
use tracing::{info_span, trace};
use uuid::uuid;

#[derive(Debug, Clone, Deserialize, Serialize, Default)]
pub struct CurrentUser {
    pub(crate) id: i32,
    pub(crate) name: String,
    pub(crate) openid: String,
    pub(crate) session_key: String,
    pub(crate) unionid: Option<String>,
    pub(crate) origin: users::Model,

}

pub async fn session_layer(pool: Arc<AppState>) -> Result<SessionLayer<SessionAnyPool>> {

    let session_config = SessionConfig::default().with_table_name("sessions_table")
        .with_ip_and_user_agent(true)
        .with_mode(SessionMode::Persistent);

    // Helper function to create SessionAnyPool based on the database backend
    let session_pool = create_session_pool(&pool)?;
    let redis_pool = pool.redis.clone();
    let session_pool2 = SessionAnyPool::new(SessionRedisPool::from(redis_pool));

    // let session_store = SessionStore::<SessionAnyPool>::new(Some(session_pool), session_config)
    //     .await
    //     .map_err(|e| anyhow::anyhow!("Failed to create session store: {}", e))?;
    let session_store2 = SessionStore::<SessionAnyPool>::new(Some(session_pool2), session_config)
        .await.unwrap();
    Ok(SessionLayer::new(session_store2))
}

// Helper function to create SessionAnyPool
fn create_session_pool(pool: &Arc<AppState>) -> Result<SessionAnyPool> {
    match pool.db.get_database_backend() {
        DatabaseBackend::MySql => Ok(SessionAnyPool::new(
            SessionMySqlPool::from(pool.db.get_mysql_connection_pool().clone()),
        )),
        DatabaseBackend::Sqlite => Ok(SessionAnyPool::new(
            SessionSqlitePool::from(pool.db.get_sqlite_connection_pool().clone()),
        )),
        DatabaseBackend::Postgres => Ok(SessionAnyPool::new(
            SessionPgPool::from(pool.db.get_postgres_connection_pool().clone()),
        )),
        _ => Err(anyhow::anyhow!("Unsupported database backend")),
    }
}

pub async fn auth(
    State(state): State<Arc<AppState>>,
    session: Session<SessionAnyPool>,
    mut request: Request<Body>,
    next: Next,
) -> Response {
    let request_id = uuid::Uuid::new_v4().to_string();
    let span = info_span!(
        "auth_handler",
        request_id = %request_id,
        message = "Handling authentication middleware"
    );
    let _guard = span.enter();

    if let Some(header) =  request.headers().get("token") {
        info!("header: {:?}", header);
        let secret_key = env::var("secret").unwrap_or("secret".to_string());
        let token = jsonwebtoken::decode::<Claims>(header.to_str().unwrap(),
                             &DecodingKey::from_secret(secret_key.as_ref()), &Validation::new(Algorithm::default()));
        if let (Ok(claim)) = token {
            info!("{:?}", claim);
            session.set("CURRENT_USER", claim.claims.user_info)
        } else {
            error!("error {:?}", token)
        }
    }
    let client = &state.redis;
    if let Ok(mut conn) = client.get_connection() {
         let _:() = conn.set("dd", "ff").unwrap();
    }
    // Simulate fetching the current user (replace with actual logic)
    if let Some(current_user) = session.get::<CurrentUser>("CURRENT_USER") {
        request.extensions_mut().insert(current_user);
        let response = next.run(request).await;
        response
    } else {
        Json(AuthResponse{message: "Not Authed".to_string()}).into_response()
    }
}

