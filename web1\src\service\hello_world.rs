use crate::router::auth::CurrentUser;
use anyhow::anyhow;
use axum::extract::{Path, Query, State};
use axum::{Extension, Json};
use entity::users;
use entity::users::Model;
use mycore::config::state::AppState;
use regex::Regex;
use sea_orm::ActiveValue::Set;
use sea_orm::{ConnectionTrait, EntityTrait};
use serde::Deserialize;
use std::sync::{Arc, LazyLock};
use tokio::sync::OnceCell;
use tracing::info;
use validator::{Validate, ValidateArgs, ValidationError};

static TEST_REGEX: LazyLock<Regex> = LazyLock::new(|| {
    info!("初始化");
    Regex::new(r"^[a-zA-Z0-9_-]{3,16}$").unwrap()
});

#[derive(Debug, Validate, Deserialize)]
pub struct Params {
    #[validate(regex(path = "*TEST_REGEX"))]
    pub name: String,
}

pub async fn hello_world(
    State(app_state): State<Arc<AppState>>,
    Path(id): Path<i32>,
    Query(params): Query<Params>,
    Extension(current_user): Extension<CurrentUser>,
) -> Json<Vec<Model>> {
    match params.validate() {
        Ok(_) => {}
        Err(e) => {
            tracing::error!("{e:?}");
            return Json(vec![]);
        }
    }
    println!("{params:?}");
    let a = app_state
        .db
        .execute_unprepared("create table users (id int, name varchar(200))")
        .await;
    info!("{:?}", current_user);
    match params.validate() {
        Ok(_) => {}
        Err(e) => {
            tracing::error!("{e:?}");
        }
    }
    if let Ok(r) = a {
        tracing::info!("{r:?}");
        tracing::info!("create table success");

        let users_vec: Vec<users::ActiveModel> = (1..1000)
            .map(|i| users::ActiveModel {
                id: Set(i),
                username: Set(format!("name{}", i)),
                password_hash: Default::default(),
                ..Default::default()
            })
            .collect();
        users::Entity::insert_many(users_vec)
            .exec(&app_state.db)
            .await
            .unwrap();
    } else if let Err(e) = a {
        tracing::info!("create table error {e:?}");
    }
    let user = users::Entity::find_by_id(id)
        .all(&app_state.db)
        .await
        .unwrap();
    Json(user)
}

