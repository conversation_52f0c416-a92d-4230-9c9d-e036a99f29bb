# NGA 爬虫配置文件示例 (Go 版本)
# 复制此文件为 config.yaml 并填写实际配置值

database:
  url: "postgresql://nga:5ee2B5h5y38FZB4i@localhost:5432/nga?sslmode=disable"
  echo: false

nga:
  prefix_url: "http://ngabbs.com/app_api.php?"
  cookie: "Hm_lvt_2728f3eacf75695538f5b1d1b5594170=1692256895,1692606172; Hm_lvt_6933ef97905336bef84f9609785bcc3d=1699838945,1699926572,1700017580,1700104318; guestJs=1718202278; lastpath=/read.php?tid=28537926&_fp=3&page=11&rand=269; ngaPassportOid=dabf6084240ae4d833acf28cb2d3f8fa; ngacn0comUserInfo=geniusLQF%09geniusLQF%0939%0939%09%0911%0930000%094%090%09272%09199_20%2C450001_10%2C450008_10%2C450010_20%2C83_30%2C-7_15; ngacn0comUserInfoCheck=c0a559059de721a0691c486741e260b9; ngacn0comInfoCheckTime=1718202289; ngaPassportUid=42951480; ngaPassportUrlencodedUname=geniusLQF; ngaPassportCid=X8sp99j03jl1l2f9rld00415ijmegbiht88vjtch; lastvisit=1718202294; bbsmisccookies=%7B%22uisetting%22%3A%7B0%3A%22c%22%2C1%3A1718202597%7D%2C%22pv_count_for_insad%22%3A%7B0%3A-46%2C1%3A1718211708%7D%2C%22insad_views%22%3A%7B0%3A1%2C1%3A1718211708%7D%7D"
  headers:
    User-Agent: "NGA_skull/6.0.7(iPhone11,6;iOS 12.2)"
    X-User-Agent: "NGA_skull/6.0.7(iPhone11,6;iOS 12.2)"
    Content-Type: "application/x-www-form-urlencoded"

bark:
  enabled: true
  server_url: "https://api.day.app"
  device_key: "twcUV9hrAjSnRLnm7DMGK9"
  default_priority: 0

log:
  level: "info"
  file_path: "./logs/nga.log"
  format: "json" 