[package]
authors = ["chaos277"]
edition = "2021"
name = "chaos-web"
version = "0.1.0"

[dependencies]
serde_json = "1.0.140"
mycore={version = "0.1.0", path = "mycore"}
web1={version = "0.1.0", path = "web1"}
web2={version = "0.1.0", path = "web2"}
app = {version = "0.1.0", path = "app"}
flame = {version = "0.2.2"}
tracing = {version = "0.1.41"}
tokio = {version = "1.45.1"}

[dev-dependencies]
tokio = "1.45.0"
serde_json = "1.0.140"
serde = "1.0.219"
tracing = "0.1.41"
tracing-subscriber = "0.3.19"

[workspace]
members = ["mycore", "web1", "web2", "mycore", "app", "migration", "wechat", "entity", "macros", "nga_rust"]
resolver = "2"

[profile.dev]
opt-level = 0

[profile.release]
opt-level = 3
lto = "thin"
codegen-units = 1
strip = false
debug = true
panic = "abort"
incremental = false
