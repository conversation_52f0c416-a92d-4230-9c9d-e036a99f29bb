"""
NGA 爬虫配置管理模块
"""
import os
from dataclasses import dataclass

from dotenv import load_dotenv

env_vars = load_dotenv()
@dataclass
class DatabaseConfig:
    """数据库配置"""
    url: str = os.getenv(
        'DATABASE_URL', 
        'postgresql+psycopg2://nga:5mXxCEFwfPidAftf@localhost:5432/nga'
    )
    echo: bool = False


@dataclass
class NGAConfig:
    """NGA API 配置"""
    prefix_url: str = "http://ngabbs.com/app_api.php?"
    headers: dict = None
    cookie: str = os.getenv('NGA_COOKIE', '')
    
    def __post_init__(self):
        if self.headers is None:
            self.headers = {
                "User-Agent": "NGA_skull/6.0.7(iPhone11,6;iOS 12.2)",
                "X-User-Agent": "NGA_skull/6.0.7(iPhone11,6;iOS 12.2)", 
                "Content-Type": "application/x-www-form-urlencoded"
            }


@dataclass
class BarkConfig:
    """Bark 推送配置"""
    enabled: bool = os.getenv('BARK_ENABLED', 'false').lower() == 'true'
    server_url: str = os.getenv('BARK_SERVER_URL', '')
    device_key: str = os.getenv('BARK_DEVICE_KEY', '')
    default_priority: int = int(os.getenv('BARK_DEFAULT_PRIORITY', '0'))


@dataclass
class LogConfig:
    """日志配置"""
    level: str = os.getenv('LOG_LEVEL', 'INFO')
    file_path: str = os.getenv('LOG_FILE', '/home/<USER>/nga.log')
    format: str = '%(asctime)s.%(msecs)03d [%(levelname)s] [%(filename)s:%(lineno)d] %(message)s'
    date_format: str = '## %Y-%m-%d %H:%M:%S'


@dataclass
class AppConfig:
    """应用配置集合"""
    database: DatabaseConfig = None
    nga: NGAConfig = None
    bark: BarkConfig = None
    log: LogConfig = None
    
    def __post_init__(self):
        if self.database is None:
            self.database = DatabaseConfig()
        if self.nga is None:
            self.nga = NGAConfig()
        if self.bark is None:
            self.bark = BarkConfig()
        if self.log is None:
            self.log = LogConfig()


# 全局配置实例
config = AppConfig() 