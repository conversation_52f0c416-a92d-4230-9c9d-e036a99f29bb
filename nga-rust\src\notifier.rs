use crate::config::BarkConfig;
use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tracing::{debug, error, warn};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BarkPayload {
    pub title: String,
    pub body: String,
    pub priority: i32,
    #[serde(flatten)]
    pub extra: HashMap<String, serde_json::Value>,
}

pub struct BarkNotifier {
    config: BarkConfig,
    client: Option<reqwest::Client>,
}

impl BarkNotifier {
    pub fn new(config: BarkConfig) -> Self {
        let client = if config.enabled {
            Some(
                reqwest::Client::builder()
                    .timeout(std::time::Duration::from_secs(10))
                    .build()
                    .unwrap_or_default(),
            )
        } else {
            None
        };

        Self { config, client }
    }

    pub async fn send_notification(
        &self,
        title: String,
        body: String,
        priority: Option<i32>,
        extra: Option<HashMap<String, serde_json::Value>>,
    ) -> Result<bool> {
        if !self.config.enabled {
            return Ok(false);
        }

        let client = match &self.client {
            Some(client) => client,
            None => return Ok(false),
        };

        if self.config.server_url.is_empty() || self.config.device_key.is_empty() {
            warn!("Bark 配置不完整，跳过推送");
            return Ok(false);
        }

        let url = format!(
            "{}/{}",
            self.config.server_url.trim_end_matches('/'),
            self.config.device_key
        );

        let payload = BarkPayload {
            title: title.chars().take(100).collect(), // 限制标题长度
            body: body.chars().take(1000).collect(),   // 限制内容长度
            priority: priority.unwrap_or(self.config.default_priority),
            extra: extra.unwrap_or_default(),
        };

        match client.post(&url).json(&payload).send().await {
            Ok(response) => {
                if response.status().is_success() {
                    debug!("Bark 推送成功: {}", payload.title);
                    Ok(true)
                } else {
                    warn!("Bark 推送失败，状态码: {}", response.status());
                    Ok(false)
                }
            }
            Err(e) => {
                error!("Bark 推送异常: {}", e);
                Ok(false)
            }
        }
    }

    pub async fn send_nga_reply_notification(
        &self,
        author: String,
        content: String,
        postdate: String,
        tid: Option<i32>,
    ) -> Result<bool> {
        let title = format!("NGA新回复 - {}", author);

        let mut body_parts = vec![
            format!("时间: {}", postdate),
            format!("内容: {}", content.chars().take(200).collect::<String>()),
        ];

        if let Some(tid) = tid {
            body_parts.push(format!("主题ID: {}", tid));
        }

        let body = body_parts.join("\n");

        self.send_notification(title, body, Some(1), None).await
    }

    pub async fn send_error_notification(&self, error_msg: String) -> Result<bool> {
        self.send_notification("NGA爬虫错误".to_string(), error_msg, Some(2), None)
            .await
    }
} 