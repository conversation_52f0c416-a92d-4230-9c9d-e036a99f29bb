package logger

import (
	"io"
	"os"
	"path/filepath"
	"strings"

	"nga-go/internal/config"

	"github.com/sirupsen/logrus"
)

var log *logrus.Logger

// Init 初始化日志系统
func Init() error {
	log = logrus.New()

	// 设置日志级别
	level, err := logrus.ParseLevel(config.AppConfig.Log.Level)
	if err != nil {
		level = logrus.InfoLevel
	}
	log.SetLevel(level)

	// 设置日志格式
	switch strings.ToLower(config.AppConfig.Log.Format) {
	case "json":
		log.SetFormatter(&logrus.JSONFormatter{
			TimestampFormat: "2006-01-02 15:04:05",
		})
	default:
		log.SetFormatter(&logrus.TextFormatter{
			FullTimestamp:   true,
			TimestampFormat: "2006-01-02 15:04:05",
		})
	}

	// 配置日志输出
	if config.AppConfig.Log.FilePath != "" {
		if err := setupFileOutput(); err != nil {
			return err
		}
	} else {
		log.SetOutput(os.Stdout)
	}

	return nil
}

// setupFileOutput 设置文件输出
func setupFileOutput() error {
	// 创建日志目录
	logDir := filepath.Dir(config.AppConfig.Log.FilePath)
	if err := os.MkdirAll(logDir, 0755); err != nil {
		return err
	}

	// 打开日志文件
	file, err := os.OpenFile(config.AppConfig.Log.FilePath,
		os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
	if err != nil {
		return err
	}

	// 同时输出到文件和控制台
	log.SetOutput(io.MultiWriter(os.Stdout, file))
	return nil
}

// Debug 调试日志
func Debug(msg string, args ...interface{}) {
	log.WithFields(parseFields(args)).Debug(msg)
}

// Info 信息日志
func Info(msg string, args ...interface{}) {
	log.WithFields(parseFields(args)).Info(msg)
}

// Warn 警告日志
func Warn(msg string, args ...interface{}) {
	log.WithFields(parseFields(args)).Warn(msg)
}

// Error 错误日志
func Error(msg string, args ...interface{}) {
	log.WithFields(parseFields(args)).Error(msg)
}

// Fatal 致命错误日志
func Fatal(msg string, args ...interface{}) {
	log.WithFields(parseFields(args)).Fatal(msg)
}

// WithField 添加单个字段
func WithField(key string, value interface{}) *logrus.Entry {
	return log.WithField(key, value)
}

// WithFields 添加多个字段
func WithFields(fields logrus.Fields) *logrus.Entry {
	return log.WithFields(fields)
}

// parseFields 解析参数为日志字段
func parseFields(args []interface{}) logrus.Fields {
	fields := logrus.Fields{}

	for i := 0; i < len(args); i += 2 {
		if i+1 < len(args) {
			key, ok := args[i].(string)
			if ok {
				fields[key] = args[i+1]
			}
		}
	}

	return fields
}

// GetLogger 获取原始 logrus 实例
func GetLogger() *logrus.Logger {
	return log
}
