mod config;
mod crawler;
mod models;
mod notifier;

use anyhow::Result;
use clap::{Parser, ValueEnum};
use config::AppConfig;
use crawler::NgaCrawler;
use notifier::BarkNotifier;
use tracing::{error, info};
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};

#[derive(Debug, Clone, ValueEnum)]
enum Mode {
    /// 爬取回复数据（默认模式）
    Reply,
    /// 爬取主题数据
    Subject,
    /// 爬取指定主题的帖子
    Post,
}

#[derive(Debug, Parser)]
#[command(name = "nga-rust")]
#[command(about = "NGA 论坛数据爬虫 Rust 版本", long_about = None)]
struct Args {
    /// 运行模式
    #[arg(short, long, value_enum, default_value = "reply")]
    mode: Mode,

    /// 主题ID（仅在post模式下使用）
    #[arg(short, long)]
    tid: Option<i32>,

    /// 最大页数（仅在subject模式下使用）
    #[arg(short, long, default_value = "5000")]
    pages: i32,

    /// 版块ID
    #[arg(short, long, default_value = "706")]
    fid: i32,

    /// 启用详细日志
    #[arg(short, long)]
    verbose: bool,
}

#[tokio::main]
async fn main() -> Result<()> {
    let args = Args::parse();

    // 初始化日志
    init_tracing(args.verbose)?;

    info!("NGA 爬虫启动中...");

    // 加载配置
    let config = AppConfig::load()?;
    info!("配置加载完成");

    // 初始化 Bark 通知器
    let notifier = if config.bark.enabled {
        Some(BarkNotifier::new(config.bark.clone()))
    } else {
        None
    };

    // 创建爬虫实例
    let crawler = NgaCrawler::new(config, notifier).await?;
    info!("爬虫初始化完成");

    // 根据模式执行不同操作
    match args.mode {
        Mode::Reply => {
            info!("开始爬取回复数据");
            crawler.process_replies().await?;
        }
        Mode::Subject => {
            info!("开始爬取主题数据，最大页数: {}", args.pages);
            crawler.process_subjects(args.pages).await?;
        }
        Mode::Post => {
            if let Some(tid) = args.tid {
                info!("开始爬取主题 {} 的帖子数据", tid);
                // 这里可以实现爬取指定主题帖子的逻辑
                match crawler.get_post_list(tid, 1).await {
                    Ok(response) => {
                        info!("获取主题 {} 的帖子数据成功", tid);
                        println!("{}", serde_json::to_string_pretty(&response)?);
                    }
                    Err(e) => {
                        error!("获取主题 {} 的帖子数据失败: {}", tid, e);
                    }
                }
            } else {
                error!("Post 模式需要指定主题ID（-t 参数）");
                std::process::exit(1);
            }
        }
    }

    info!("爬虫任务完成");
    Ok(())
}

fn init_tracing(verbose: bool) -> Result<()> {
    let level = if verbose { "debug" } else { "info" };
    
    tracing_subscriber::registry()
        .with(
            tracing_subscriber::EnvFilter::try_from_default_env()
                .unwrap_or_else(|_| format!("nga_rust={},tower_http=debug", level).into()),
        )
        .with(tracing_subscriber::fmt::layer())
        .init();

    Ok(())
} 