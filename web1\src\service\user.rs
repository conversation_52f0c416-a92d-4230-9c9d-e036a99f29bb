use crate::router::auth::CurrentUser;
use axum::body::Body;
use axum::extract::Request;
use axum::http::HeaderMap;
use axum::response::Response;
use axum::{
    extract::{Json, State},
    http::StatusCode,
    response::IntoResponse,
};
use axum_session::{Session, SessionAnyPool};
use bcrypt::{hash, verify, DEFAULT_COST};
use chrono::{Local, Timelike, Utc};
use entity::users;
use jsonwebtoken::{Enco<PERSON><PERSON><PERSON>, Header};
use log::{error, info};
use mycore::config::state::AppState;
use sea_orm::sea_query::ColumnSpec::AutoIncrement;
use sea_orm::{ActiveModelTrait, ColumnTrait, EntityTrait, IntoActiveModel, NotSet, QueryFilter, Set, TryIntoModel};
use serde::{Deserialize, Serialize};
use serde_json::{json, <PERSON>};
use std::default::Default;
use std::env;
use std::sync::Arc;
use wechat::mp::WechatAuthorize;
use wechat::weapp::WxLoginSession;
use wechat::{watch_time, weapp, ErrorKind, WechatError, WechatResult};

#[derive(Deserialize)]
pub struct RegisterRequest {
    pub username: String,
    pub password: String,
}

#[derive(Deserialize)]
pub struct LoginRequest {
    pub username: String,
    pub password: String,
}

#[derive(Serialize)]
pub struct AuthResponse {
    pub message: String,
}

// User registration handler
pub async fn register(
    State(state): State<Arc<AppState>>,
    Json(payload): Json<RegisterRequest>,
) -> impl IntoResponse {
    // Hash the password
    let password_hash = match hash(&payload.password, 10) {
        Ok(hash) => hash,
        Err(_) => return (StatusCode::INTERNAL_SERVER_ERROR, Json(AuthResponse { message: "Failed to hash password".to_string() })),
    };

    let existUser = users::Entity::find()
        .filter(users::Column::Username.eq(&payload.username))
        .one(&state.db)
        .await;

    if let Ok(Some(user)) = existUser {
        return (StatusCode::BAD_REQUEST, Json(AuthResponse{message: ("Exist username: ".to_string() + &user.username).to_string() }));
    }


    // Insert user into the database
    let user = users::ActiveModel {
        username: Set(payload.username),
        password_hash: Set(password_hash),
        ..Default::default()
    };
    let result = users::Entity::insert(user).exec(
        &state.db
    ).await;

    match result {
        Ok(_) => (StatusCode::CREATED, Json(AuthResponse { message: "User registered successfully".to_string() })),
        Err(e) => {
            error!("failed register user :{}", e);
            (StatusCode::INTERNAL_SERVER_ERROR, Json(AuthResponse { message: "Failed to register user".to_string() }))
        },
    }
}

// User login handler
pub async fn login(
    State(state): State<Arc<AppState>>,
    session: Session<SessionAnyPool>,
    Json(payload): Json<LoginRequest>
) -> impl IntoResponse {
    // Fetch user from the database
    let user = users::Entity::find()
        .filter(users::Column::Username.eq(payload.username))
        .one(&state.db)
        .await;
    let mut header_map = HeaderMap::new();

    let user = match user {
        Ok(Some(user)) => user,
        _ => return (StatusCode::UNAUTHORIZED, header_map, Json(AuthResponse { message: "Invalid username or password".to_string() })),
    };
    let secret_key = env::var("secret").unwrap_or("secret".to_string());

    // Verify the password
    if let Ok(is_valid) = watch_time!("verify password", verify(&payload.password, &user.password_hash)) {
        if is_valid {
            let mut current_user = CurrentUser {
                id: user.id,
                name: user.username.clone(),
                origin: user.clone(),
                ..Default::default()
            };
            session.set("CURRENT_USER", current_user.clone());
            current_user.origin.password_hash = "".to_string();
            if let Ok(token) = jsonwebtoken::encode(&Header::default(), &Claims{
                user_info: current_user,
                exp: (Local::now().timestamp() + 3600) as usize, // 1 hour expiration
                iat: Local::now().timestamp() as usize,
                iss: "myapp".to_string(),
                nbf: Local::now().timestamp() as usize,
                sub: user.username.clone(),
            }, &EncodingKey::from_secret(secret_key.as_ref())) {
                header_map.append("token", token.parse().unwrap());
            }
            return (StatusCode::OK, header_map, Json(AuthResponse { message: "Login successful".to_string() }));
        }
    }

    (StatusCode::UNAUTHORIZED,  header_map, Json(AuthResponse { message: "Invalid username or password".to_string() }))
}


pub async fn wx_login(State(state): State<Arc<AppState>>, session: Session<SessionAnyPool>, Json(req): Json<WxReq>) -> impl IntoResponse {
    let api_id =  env::var("WX_APP_ID").unwrap_or("wx_app_id".to_string());
    let api_secret = env::var("WX_APP_SECRET").unwrap_or("wx_app_secret".to_string());
    let res = weapp::Auth::get_session_key(&api_id, &api_secret, &req.code).await;
    if let Ok(data) = &res {
        let res = users::Entity::find()
            .filter(users::Column::Openid.eq(&data.openid))
            .one(&state.db)
            .await;
        let current_user;
        if let (Ok(Some(user))) = res {
            let user = users::ActiveModel {
                id: Set(user.id),
                username: Set(req.name),
                avatar_url: Set(Some(req.avatar_url.clone())),
                session_key: Set(Some(data.session_key.clone())),
                unionid: Set(data.unionid.clone()),
                ..Default::default()
            };
            let model = user.update(&state.db).await;
            current_user = model.unwrap().into_active_model();
        } else {
            // Hash the password
            let password_hash = match hash(&data.openid, 10) {
                Ok(hash) => hash,
                Err(_) => return  WechatRes::Err(WechatErr::new(401, "Failed to hash password".to_string()))
            };
            // Insert user into the database
            let user = users::ActiveModel {
                id: NotSet,
                username: Set(req.name.clone()),
                password_hash: Set(password_hash),
                openid: Set(Some(data.openid.clone())),
                session_key: Set(Some(data.session_key.clone())),
                unionid: Set(data.unionid.clone()),
                phone: NotSet,
                avatar_url: Set(Some(req.avatar_url.clone())),
            };
            let model = user.save(&state.db).await.unwrap();
            current_user = model;
        }
        session.set("CURRENT_USER", CurrentUser{
            id: current_user.id.clone().unwrap(),
            name: current_user.username.clone().unwrap(),
            openid: (data.openid).to_string(),
            unionid: (data.unionid).clone(),
            session_key: (data.session_key).to_string(),
            origin: current_user.clone().try_into_model().unwrap()
        });
    }
    WechatRes::new(res)
}

#[derive(Deserialize)]
pub struct WxReq {
    name: String,
    avatar_url: String,
    code: String
}

#[derive(Debug, Serialize, Deserialize)]
pub(crate) struct Claims {
    // pub aud: String,         // Optional. Audience
    pub exp: usize,          // Required (validate_exp defaults to true in validation). Expiration time (as UTC timestamp)
    pub iat: usize,          // Optional. Issued at (as UTC timestamp)
    pub iss: String,         // Optional. Issuer
    pub nbf: usize,          // Optional. Not Before (as UTC timestamp)
    pub sub: String,         // Optional. Subject (whom token refers to)
    pub user_info: CurrentUser
}
pub type WechatRes<T> = Result<T, WechatErr>;

trait new<T> {
    fn new(res: WechatResult<T>) -> WechatRes<Json<T>>;
}
impl <T> new<T> for WechatRes<T> {
    fn new(res: WechatResult<T>) -> WechatRes<Json<T>> {
        match res {
            Ok(data) => WechatRes::Ok(Json(data)),
            Err(e) => WechatRes::Err(WechatErr(e))
        }
    }
}

struct WechatErr(WechatError);

impl WechatErr {
    pub fn new(code: i32, msg: String) -> Self {
        WechatErr(WechatError::custom(code, msg))
    }
}

impl IntoResponse for WechatErr {
    fn into_response(self) -> Response {
        match self.0.kind {
            ErrorKind::Msg(msg) => (StatusCode::INTERNAL_SERVER_ERROR, msg).into_response(),
            ErrorKind::Custom {code, msg} => (StatusCode::INTERNAL_SERVER_ERROR, msg).into_response(),
            ErrorKind::Io(err) => (StatusCode::INTERNAL_SERVER_ERROR, "error").into_response(),
        }

    }
}