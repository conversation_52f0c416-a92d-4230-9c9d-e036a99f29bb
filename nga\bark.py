import httpx
import logging
from typing import Optional

class BarkNotifier:
    def __init__(self, bark_server_url: str, device_key: str):
        self.bark_server_url = bark_server_url.rstrip('/')
        self.device_key = device_key
        self.client = httpx.Client()

    def send_notification(self, title: str, body: str, priority: int = 0) -> bool:
        """Send a notification via Bark."""
        url = f"{self.bark_server_url}/{self.device_key}"
        payload = {
            "title": title,
            "body": body,
            "priority": priority
        }
        try:
            resp = self.client.post(url, json=payload)
            return resp.status_code == 200
        except Exception as e:
            logging.error(f"Bark推送失败: {e}")
            return False
