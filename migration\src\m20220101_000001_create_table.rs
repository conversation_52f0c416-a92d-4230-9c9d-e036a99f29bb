use crate::ColumnSpec::Null;
use entity::users;
use sea_orm_migration::{prelude::*, schema::*};

#[derive(DeriveMigrationName)]
pub struct Migration;

#[async_trait::async_trait]
impl MigrationTrait for Migration {
    async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        // Replace the sample below with your own migration scripts
        manager.create_table(
            Table::create()
                .table(users::Entity)
                .if_not_exists()
                .col(pk_auto(users::Column::Id))
                .col(string(users::Column::Username))
                .col(string(users::Column::PasswordHash))
                .col(string_null(users::Column::AvatarUrl))
                .col(string_null(users::Column::Openid))
                .col(string_null(users::Column::Unionid))
                .col(string_null(users::Column::Phone))
                .col(string_null(users::Column::<PERSON><PERSON><PERSON>))
                .to_owned(),
        ).await
        // manager
        //     .create_table(
        //         Table::create()
        //             .table(Post::Table)
        //             .if_not_exists()
        //             .col(pk_auto(Post::Id))
        //             .col(string(Post::Title))
        //             .col(string(Post::Text))
        //             .to_owned(),
        //
        //     )
        //     .await
    }

    async fn down(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        // Replace the sample below with your own migration scripts
        manager
            .drop_table(Table::drop().if_exists().table(Post::Table).to_owned())
            .await;
        manager.drop_table(Table::drop().if_exists().table(users::Entity).to_owned())
            .await
    }
}

#[derive(DeriveIden)]
enum Post {
    Table,
    Id,
    Title,
    Text,
}

