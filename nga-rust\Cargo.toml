[package]
name = "nga-rust"
version = "0.1.0"
edition = "2021"
authors = ["NGA Crawler"]
description = "NGA 论坛数据爬虫 Rust 版本"


[dependencies]
tokio = { version = "1.0", features = ["full"] }
reqwest = { version = "0.11", features = ["json", "cookies"] }
sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "postgres", "chrono", "uuid"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
dotenvy = "0.15"
anyhow = "1.0"
thiserror = "1.0"
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }
chrono = { version = "0.4", features = ["serde"] }
regex = "1.0"
clap = { version = "4.0", features = ["derive"] }
uuid = { version = "1.0", features = ["serde"] }

[dev-dependencies]
tokio-test = "0.4" 