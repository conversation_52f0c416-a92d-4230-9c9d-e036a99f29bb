# NGA 爬虫配置文件示例
# 复制此文件为 .env 并填写实际配置值

# 数据库配置
DATABASE_URL=postgresql+psycopg2://username:password@localhost:5432/nga

# NGA Cookie (必需，从浏览器中获取)
NGA_COOKIE=your_nga_cookie_here

# Bark 推送配置
BARK_ENABLED=true
BARK_SERVER_URL=https://api.day.app
BARK_DEVICE_KEY=your_device_key_here
BARK_DEFAULT_PRIORITY=0

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=/home/<USER>/nga.log

# 使用说明：
# 1. 将此文件重命名为 .env
# 2. 填写你的实际配置值
# 3. 确保 .env 文件不会被提交到版本控制系统 