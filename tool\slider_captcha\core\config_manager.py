"""
配置管理器

负责加载、验证和管理滑块验证码系统的各种配置参数。
支持YAML和JSON格式的配置文件，提供默认配置和配置热加载功能。
"""

import os
import logging
from typing import Dict, Any, Optional, Union
from pathlib import Path


class ConfigManager:
    """配置管理器"""
    
    # 默认配置
    DEFAULT_CONFIG = {
        'opencv': {
            'gaussian_blur_kernel': (5, 5),
            'canny_threshold1': 200,
            'canny_threshold2': 400,
            'contour_area_range': (6000, 8000),
            'contour_arc_length_range': (370, 390),
            'image_scale_factor': 0.5,
            'min_confidence': 0.7
        },
        'trajectory': {
            'initial_velocity': 0,
            'max_acceleration': 3,
            'deceleration': -2,
            'time_step': 0.2,
            'overshoot_distance': 10,
            'backtrack_count': 4,
            'backtrack_range': (1, 3),
            'smoothing_factor': 0.1,
            'jitter_strength': 0.1
        },
        'selenium': {
            'headless': False,
            'window_size': (1366, 768),
            'page_load_timeout': 30,
            'element_timeout': 10,
            'stealth_mode': True,
            'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        },
        'captcha': {
            'max_retry_attempts': 3,
            'retry_delay': 2.0,
            'success_threshold': 0.8,
            'debug_mode': False,
            'save_debug_images': False
        },
        'logging': {
            'level': 'INFO',
            'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            'file_path': None  # None表示只输出到控制台
        }
    }
    
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            config_path: 配置文件路径，如果为None则使用默认配置
        """
        self.logger = logging.getLogger(self.__class__.__name__)
        self._config = self.DEFAULT_CONFIG.copy()
        self._config_path = config_path
        
        if config_path:
            self.load_config(config_path)
        else:
            self.logger.info("使用默认配置")
            
        self._setup_logging()
    
    def load_config(self, config_path: str) -> None:
        """
        从文件加载配置
        
        Args:
            config_path: 配置文件路径
            
        Raises:
            FileNotFoundError: 配置文件不存在
            ValueError: 配置文件格式错误
        """
        config_path = Path(config_path)
        
        if not config_path.exists():
            raise FileNotFoundError(f"配置文件不存在: {config_path}")
            
        try:
            if config_path.suffix.lower() in ['.yml', '.yaml']:
                config_data = self._load_yaml(config_path)
            elif config_path.suffix.lower() == '.json':
                config_data = self._load_json(config_path)
            else:
                raise ValueError(f"不支持的配置文件格式: {config_path.suffix}")
                
            # 合并配置（用户配置覆盖默认配置）
            self._merge_config(config_data)
            self._config_path = str(config_path)
            
            self.logger.info(f"成功加载配置文件: {config_path}")
            
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            raise
    
    def _load_yaml(self, config_path: Path) -> Dict[str, Any]:
        """加载YAML配置文件"""
        try:
            import yaml
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f) or {}
        except ImportError:
            raise ImportError("请安装PyYAML库: pip install PyYAML")
    
    def _load_json(self, config_path: Path) -> Dict[str, Any]:
        """加载JSON配置文件"""
        import json
        with open(config_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def _merge_config(self, user_config: Dict[str, Any]) -> None:
        """
        合并用户配置到默认配置
        
        Args:
            user_config: 用户配置字典
        """
        def deep_merge(base_dict: Dict, update_dict: Dict) -> Dict:
            """深度合并字典"""
            result = base_dict.copy()
            
            for key, value in update_dict.items():
                if (key in result and 
                    isinstance(result[key], dict) and 
                    isinstance(value, dict)):
                    result[key] = deep_merge(result[key], value)
                else:
                    result[key] = value
                    
            return result
        
        self._config = deep_merge(self._config, user_config)
        self._validate_config()
    
    def _validate_config(self) -> None:
        """验证配置参数的有效性"""
        # 验证OpenCV配置
        opencv_config = self._config.get('opencv', {})
        if 'gaussian_blur_kernel' in opencv_config:
            kernel = opencv_config['gaussian_blur_kernel']
            if not (isinstance(kernel, (list, tuple)) and len(kernel) == 2):
                raise ValueError("gaussian_blur_kernel必须是长度为2的元组或列表")
        
        # 验证轨迹配置  
        trajectory_config = self._config.get('trajectory', {})
        if 'time_step' in trajectory_config:
            time_step = trajectory_config['time_step']
            if not (0.01 <= time_step <= 1.0):
                raise ValueError("time_step必须在0.01到1.0之间")
        
        # 验证Selenium配置
        selenium_config = self._config.get('selenium', {})
        if 'window_size' in selenium_config:
            window_size = selenium_config['window_size']
            if not (isinstance(window_size, (list, tuple)) and len(window_size) == 2):
                raise ValueError("window_size必须是长度为2的元组或列表")
        
        self.logger.debug("配置验证通过")
    
    def _setup_logging(self) -> None:
        """根据配置设置日志"""
        logging_config = self._config.get('logging', {})
        
        level = getattr(logging, logging_config.get('level', 'INFO').upper())
        format_str = logging_config.get('format', '%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        
        # 配置根日志记录器
        logging.basicConfig(
            level=level,
            format=format_str,
            handlers=[]
        )
        
        # 添加控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(logging.Formatter(format_str))
        logging.getLogger().addHandler(console_handler)
        
        # 添加文件处理器（如果配置了文件路径）
        file_path = logging_config.get('file_path')
        if file_path:
            try:
                file_handler = logging.FileHandler(file_path, encoding='utf-8')
                file_handler.setFormatter(logging.Formatter(format_str))
                logging.getLogger().addHandler(file_handler)
                self.logger.info(f"日志将保存到文件: {file_path}")
            except Exception as e:
                self.logger.warning(f"无法创建日志文件处理器: {e}")
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值，支持点号分隔的嵌套键
        
        Args:
            key: 配置键，支持'opencv.canny_threshold1'格式
            default: 默认值
            
        Returns:
            Any: 配置值
        """
        keys = key.split('.')
        current = self._config
        
        try:
            for k in keys:
                current = current[k]
            return current
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any) -> None:
        """
        设置配置值，支持点号分隔的嵌套键
        
        Args:
            key: 配置键
            value: 配置值
        """
        keys = key.split('.')
        current = self._config
        
        for k in keys[:-1]:
            if k not in current:
                current[k] = {}
            current = current[k]
        
        current[keys[-1]] = value
        self.logger.debug(f"配置已更新: {key} = {value}")
    
    def get_section(self, section: str) -> Dict[str, Any]:
        """
        获取配置段
        
        Args:
            section: 配置段名称
            
        Returns:
            Dict[str, Any]: 配置段字典
        """
        return self._config.get(section, {}).copy()
    
    def save_config(self, save_path: Optional[str] = None) -> None:
        """
        保存当前配置到文件
        
        Args:
            save_path: 保存路径，如果为None则保存到原配置文件
        """
        target_path = save_path or self._config_path
        
        if not target_path:
            raise ValueError("没有指定保存路径，且没有原始配置文件路径")
        
        target_path = Path(target_path)
        
        try:
            if target_path.suffix.lower() in ['.yml', '.yaml']:
                self._save_yaml(target_path)
            elif target_path.suffix.lower() == '.json':
                self._save_json(target_path)
            else:
                # 默认保存为YAML格式
                target_path = target_path.with_suffix('.yaml')
                self._save_yaml(target_path)
                
            self.logger.info(f"配置已保存到: {target_path}")
            
        except Exception as e:
            self.logger.error(f"保存配置失败: {e}")
            raise
    
    def _save_yaml(self, save_path: Path) -> None:
        """保存为YAML格式"""
        try:
            import yaml
            with open(save_path, 'w', encoding='utf-8') as f:
                yaml.dump(self._config, f, default_flow_style=False, 
                         allow_unicode=True, indent=2)
        except ImportError:
            raise ImportError("请安装PyYAML库: pip install PyYAML")
    
    def _save_json(self, save_path: Path) -> None:
        """保存为JSON格式"""
        import json
        with open(save_path, 'w', encoding='utf-8') as f:
            json.dump(self._config, f, ensure_ascii=False, indent=2)
    
    def reload_config(self) -> None:
        """重新加载配置文件"""
        if self._config_path:
            self.load_config(self._config_path)
        else:
            self.logger.warning("没有配置文件路径，无法重新加载")
    
    def get_all_config(self) -> Dict[str, Any]:
        """获取所有配置"""
        return self._config.copy()
    
    def reset_to_default(self) -> None:
        """重置为默认配置"""
        self._config = self.DEFAULT_CONFIG.copy()
        self.logger.info("配置已重置为默认值")
    
    def __str__(self) -> str:
        """返回配置管理器的字符串表示"""
        config_source = self._config_path or "默认配置"
        return f"ConfigManager(source={config_source})"
    
    def __repr__(self) -> str:
        """返回配置管理器的详细字符串表示"""
        return f"ConfigManager(config_path={self._config_path})" 