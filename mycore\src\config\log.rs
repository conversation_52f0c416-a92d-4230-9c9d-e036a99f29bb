use std::cell::<PERSON>zyCell;
use std::sync::{Arc, LazyLock};
use tracing::Level;
use tracing_subscriber::fmt::time::ChronoLocal;
use tracing_subscriber::util::SubscriberInitExt;

static LOG_INIT: LazyLock<bool> = LazyLock::new(|| {
    tracing_subscriber::fmt()
        .with_max_level(Level::INFO)
        .with_thread_names(true)
        .with_thread_ids(true)
        .with_ansi(false)
        .with_timer(ChronoLocal::rfc_3339())
        .try_init();
    true
});

pub fn init_log() {
    dotenv::dotenv().ok();
    if *LOG_INIT {
        tracing::info!("Logging initialized successfully.");
    } else {
        tracing::warn!("Logging was already initialized.");
    }
}
