<?xml version="1.0" encoding="UTF-8"?>
<module type="JAVA_MODULE" version="4">
  <component name="Go" enabled="true" />
  <component name="NewModuleRootManager" inherit-compiler-output="true">
    <exclude-output />
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/app/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/migration/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/mycore/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/web1/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/web2/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/wechat/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/entity/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/macros/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/nga-rust/src" isTestSource="false" />
      <excludeFolder url="file://$MODULE_DIR$/nga-rust/target" />
      <excludeFolder url="file://$MODULE_DIR$/target" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
  </component>
</module>