use crate::config::{AppConfig, NgaConfig};
use crate::models::*;
use crate::notifier::BarkNotifier;
use anyhow::{anyhow, Result};
use regex::Regex;
use serde_json::Value;
use std::collections::HashMap;
use std::sync::Arc;
use tracing::{debug, error, info, warn};

pub struct NgaCrawler {
    config: NgaConfig,
    client: reqwest::Client,
    notifier: Option<Arc<BarkNotifier>>,
    pool: sqlx::PgPool,
    
    // 预编译正则表达式，提高性能
    trim_quote: Regex,
    trim_b: Regex,
    trim_br: Regex,
    replace_url: Regex,
}

impl NgaCrawler {
    pub async fn new(config: AppConfig, notifier: Option<BarkNotifier>) -> Result<Self> {
        let mut headers = reqwest::header::HeaderMap::new();
        
        for (key, value) in &config.nga.headers {
            let header_name = reqwest::header::HeaderName::from_bytes(key.as_bytes())?;
            let header_value = reqwest::header::HeaderValue::from_str(value)?;
            headers.insert(header_name, header_value);
        }

        if !config.nga.cookie.is_empty() {
            let cookie_value = reqwest::header::HeaderValue::from_str(&config.nga.cookie)?;
            headers.insert(reqwest::header::COOKIE, cookie_value);
        }

        let client = reqwest::Client::builder()
            .default_headers(headers)
            .timeout(std::time::Duration::from_secs(30))
            .build()?;

        let pool = sqlx::PgPool::connect(&config.database.url).await?;

        // 创建表
        NgaAuthor::create_table(&pool).await?;
        NgaSubject::create_table(&pool).await?;

        // 预编译正则表达式
        let trim_quote = Regex::new(r"^\[quote].*?\[uid.*?](.*)\[\/uid].*?<br\/>(.*?)\[\/quote]")?;
        let trim_b = Regex::new(r"^\[b].*?\[uid.*?](.*)\[\/uid].*?\[\/b](.*?)")?;
        let trim_br = Regex::new(r"<br\/>|\n")?;
        let replace_url = Regex::new(r"\[img]\.(.*)\.jpg\[\/img]")?;

        Ok(Self {
            config: config.nga,
            client,
            notifier: notifier.map(Arc::new),
            pool,
            trim_quote,
            trim_b,
            trim_br,
            replace_url,
        })
    }

    async fn make_request(&self, endpoint: &str, data: HashMap<String, String>) -> Result<Value> {
        let url = format!("{}{}", self.config.prefix_url, endpoint);
        
        match self.client.post(&url).form(&data).send().await {
            Ok(response) => {
                if response.status().is_success() {
                    let json: Value = response.json().await?;
                    Ok(json)
                } else {
                    Err(anyhow!("HTTP error: {}", response.status()))
                }
            }
            Err(e) => {
                error!("请求失败 {}: {}", endpoint, e);
                if let Some(notifier) = &self.notifier {
                    notifier.send_error_notification(format!("API请求失败: {}", e)).await?;
                }
                Err(e.into())
            }
        }
    }

    pub async fn get_subject_list(&self, fid: i32, page: i32) -> Result<Value> {
        let mut data = HashMap::new();
        data.insert("fid".to_string(), fid.to_string());
        data.insert("page".to_string(), page.to_string());
        
        self.make_request("__lib=subject&__act=list", data).await
    }

    pub async fn get_post_list(&self, tid: i32, page: i32) -> Result<Value> {
        let mut data = HashMap::new();
        data.insert("tid".to_string(), tid.to_string());
        data.insert("page".to_string(), page.to_string());
        
        self.make_request("__lib=post&__act=list", data).await
    }

    pub async fn get_reply_list(&self, uid: i32, page: i32) -> Result<Value> {
        let mut data = HashMap::new();
        data.insert("uid".to_string(), uid.to_string());
        data.insert("page".to_string(), page.to_string());
        
        self.make_request("__lib=user&__act=replys", data).await
    }

    fn format_content(&self, content: &str) -> String {
        let mut result = content.to_string();
        
        // 处理引用
        if let Some(captures) = self.trim_quote.captures(&result) {
            if let (Some(user), Some(text)) = (captures.get(1), captures.get(2)) {
                result = format!("({}){}", user.as_str(), text.as_str());
            }
        }
        
        // 处理粗体
        if let Some(captures) = self.trim_b.captures(&result) {
            if let (Some(user), Some(text)) = (captures.get(1), captures.get(2)) {
                result = format!("({}){}", user.as_str(), text.as_str());
            }
        }
        
        // 处理换行
        result = self.trim_br.replace_all(&result, " ").to_string();
        
        result
    }

    pub async fn process_subjects(&self, max_pages: i32) -> Result<()> {
        info!("开始处理主题数据，最大页数: {}", max_pages);
        
        for page in 1..=max_pages {
            match self.get_subject_list(706, page).await {
                Ok(response) => {
                    if let Some(data) = response.get("data") {
                        if let Some(rows) = data.get("__ROWS") {
                            if let Some(rows_obj) = rows.as_object() {
                                for (_, subject_data) in rows_obj {
                                    if let Ok(subject) = self.parse_subject(subject_data) {
                                        if let Err(e) = subject.insert_or_update(&self.pool).await {
                                            error!("插入主题数据失败: {}", e);
                                        }
                                    }
                                }
                            }
                        }
                    }
                    
                    debug!("处理第 {} 页主题数据完成", page);
                    
                    // 添加延迟避免请求过于频繁
                    tokio::time::sleep(std::time::Duration::from_millis(500)).await;
                }
                Err(e) => {
                    warn!("获取第 {} 页主题数据失败: {}", page, e);
                    continue;
                }
            }
        }
        
        info!("主题数据处理完成");
        Ok(())
    }

    pub async fn process_replies(&self) -> Result<()> {
        info!("开始处理回复数据");
        
        let authors: Vec<NgaAuthor> = sqlx::query_as(
            "SELECT * FROM nga_author WHERE sub = 1"
        )
        .fetch_all(&self.pool)
        .await?;

        for author in authors {
            if let Err(e) = self.process_author_replies(&author).await {
                error!("处理用户 {} 的回复失败: {}", author.username.unwrap_or_default(), e);
            }
            
            // 添加延迟
            tokio::time::sleep(std::time::Duration::from_secs(1)).await;
        }
        
        info!("回复数据处理完成");
        Ok(())
    }

    async fn process_author_replies(&self, author: &NgaAuthor) -> Result<()> {
        if let Some(uid) = author.uid {
            match self.get_reply_list(uid, 1).await {
                Ok(response) => {
                    if let Some(data) = response.get("data") {
                        if let Some(rows) = data.get("__ROWS") {
                            if let Some(rows_obj) = rows.as_object() {
                                for (_, reply_data) in rows_obj {
                                    if let Ok(post) = self.parse_reply(reply_data) {
                                        // 检查是否是新回复
                                        if self.is_new_reply(&post, author).await? {
                                            // 发送通知
                                            if let Some(notifier) = &self.notifier {
                                                if let (Some(author_name), Some(content), Some(postdate)) = 
                                                    (&post.author, &post.content, &post.postdate) {
                                                    let formatted_content = self.format_content(content);
                                                    notifier.send_nga_reply_notification(
                                                        author_name.clone(),
                                                        formatted_content,
                                                        postdate.format("%Y-%m-%d %H:%M:%S").to_string(),
                                                        post.tid,
                                                    ).await?;
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                Err(e) => {
                    warn!("获取用户 {} 回复失败: {}", uid, e);
                }
            }
        }
        
        Ok(())
    }

    fn parse_subject(&self, data: &Value) -> Result<NgaSubject> {
        Ok(NgaSubject {
            id: None,
            tid: data.get("tid").and_then(|v| v.as_i64()).map(|v| v as i32),
            fid: data.get("fid").and_then(|v| v.as_i64()).map(|v| v as i32),
            author: data.get("author").and_then(|v| v.as_str()).map(|s| s.to_string()),
            authorid: data.get("authorid").and_then(|v| v.as_i64()).map(|v| v as i32),
            subject: data.get("subject").and_then(|v| v.as_str()).map(|s| s.to_string()),
            postdate: data.get("postdate")
                .and_then(|v| v.as_i64())
                .and_then(|ts| chrono::DateTime::from_timestamp(ts, 0)),
            lastpost: data.get("lastpost")
                .and_then(|v| v.as_i64())
                .and_then(|ts| chrono::DateTime::from_timestamp(ts, 0)),
            lastposter: data.get("lastposter").and_then(|v| v.as_str()).map(|s| s.to_string()),
            replies: data.get("replies").and_then(|v| v.as_i64()).map(|v| v as i32),
        })
    }

    fn parse_reply(&self, data: &Value) -> Result<NgaPost> {
        Ok(NgaPost {
            tid: data.get("tid").and_then(|v| v.as_i64()).map(|v| v as i32),
            pid: data.get("pid").and_then(|v| v.as_i64()).map(|v| v as i32),
            author: data.get("author").and_then(|v| v.as_str()).map(|s| s.to_string()),
            authorid: data.get("authorid").and_then(|v| v.as_i64()).map(|v| v as i32),
            postdate: data.get("postdate")
                .and_then(|v| v.as_i64())
                .and_then(|ts| chrono::DateTime::from_timestamp(ts, 0)),
            content: data.get("content").and_then(|v| v.as_str()).map(|s| s.to_string()),
        })
    }

    async fn is_new_reply(&self, _post: &NgaPost, _author: &NgaAuthor) -> Result<bool> {
        // 这里可以实现检查是否为新回复的逻辑
        // 暂时返回 true，实际应该检查数据库中是否已存在
        Ok(true)
    }
} 