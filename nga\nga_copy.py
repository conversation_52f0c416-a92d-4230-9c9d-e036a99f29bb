import math
import datetime
import random
import time

import httpx
import sqlalchemy
from sqlalchemy import Column, Integer, String, TIMESTAMP, UniqueConstraint
from sqlalchemy.orm import declarative_base, sessionmaker
from sqlalchemy.dialects.mysql import insert
import logging

logging.basicConfig(level=logging.DEBUG)

Base = declarative_base()

class NGASubject(Base):
    __tablename__ = 'nga_subjects'

    id = Column(Integer, primary_key=True, comment='主键ID')
    tid = Column(Integer, comment='主题ID', unique=True)
    fid = Column(Integer, comment='版块ID')
    author = Column(String(200), comment='作者')
    authorid = Column(Integer, comment='作者ID')
    subject = Column(String(200), comment='主题')
    postdate = Column(TIMESTAMP, comment='发表时间')
    lastpost = Column(TIMESTAMP, comment='最后发表时间')
    lastposter = Column(String(200), comment='最后发表作者')
    replies = Column(Integer, comment='回复数')

    UniqueConstraint('tid', 'fid', name='uq_tid_fid')

class NGA:
    prefix_url = "http://ngabbs.com/app_api.php?"
    headers = {
        "User-Agent": "NGA_skull/6.0.7(iPhone11,6;iOS 12.2)",
        "X-User-Agent": "NGA_skull/6.0.7(iPhone11,6;iOS 12.2)",
        "Content-Type": "application/x-www-form-urlencoded"
    }
    def __init__(self, **kwargs):
        self.headers['cookie'] = kwargs.get('cookie')
        self.client = httpx.Client(headers=self.headers)

    def get_subject_list(self, fid=706, page=1):
        with open("subject.json", 'w') as f:
            content = self.client.post(self.prefix_url + "__lib=subject&__act=list", data={"fid": fid, "page": page})
            f.write(content.text)
            return content.json()

    def get_post_list(self, fid=706, page=1):
        with open("post.json", 'w') as f:
            content = self.client.post(self.prefix_url + "__lib=post&__act=list", data={"fid": fid, "page": page})
            f.write(content.text)
            return content.json()

nga = NGA(cookie="Hm_lvt_2728f3eacf75695538f5b1d1b5594170=1692256895,1692606172; Hm_lvt_6933ef97905336bef84f9609785bcc3d=1699838945,1699926572,1700017580,1700104318; guestJs=1718202278; lastpath=/read.php?tid=28537926&_fp=3&page=11&rand=269; ngaPassportOid=dabf6084240ae4d833acf28cb2d3f8fa; ngacn0comUserInfo=geniusLQF%09geniusLQF%0939%0939%09%0911%0930000%094%090%09272%09199_20%2C450001_10%2C450008_10%2C450010_20%2C83_30%2C-7_15; ngacn0comUserInfoCheck=c0a559059de721a0691c486741e260b9; ngacn0comInfoCheckTime=1718202289; ngaPassportUid=42951480; ngaPassportUrlencodedUname=geniusLQF; ngaPassportCid=X8sp99j03jl1l2f9rld00415ijmegbiht88vjtch; lastvisit=1718202294; bbsmisccookies=%7B%22uisetting%22%3A%7B0%3A%22c%22%2C1%3A1718202597%7D%2C%22pv_count_for_insad%22%3A%7B0%3A-46%2C1%3A1718211708%7D%2C%22insad_views%22%3A%7B0%3A1%2C1%3A1718211708%7D%7D")



if __name__ == '__main__':
    engine = sqlalchemy.create_engine("mysql+pymysql://nga:5mXxCEFwfPidAftf@localhost:3306/nga?charset=utf8mb4", echo=False)
    Session = sessionmaker(bind=engine, autoflush=True)
    session = Session()
    Base.metadata.create_all(engine)
    resp = nga.get_post_list()
    sys.exit(1)
    for i in range(1, 50):
        resp = nga.get_subject_list(page=i)
        for data in resp['result']['data']:
            nga_subject = {k: v for k, v in data.items() if hasattr(NGASubject, k)}
            nga_subject['postdate'] = datetime.datetime.fromtimestamp(nga_subject['postdate'], datetime.timezone(offset=datetime.timedelta(hours=8)))
            nga_subject['lastpost'] = datetime.datetime.fromtimestamp(nga_subject['lastpost'], datetime.timezone(offset=datetime.timedelta(hours=8)))
            stmt = insert(NGASubject).values(nga_subject).on_duplicate_key_update(**nga_subject)
            session.execute(stmt)
            session.commit()
        time.sleep(random.randint(1, 5))


