// mod app;
// mod mem_test;

mod main_test;

use log::info;
use mycore::config::log::init_log;
use mycore::config::rt::{RT, RT3, RT4};
use std::cell::LazyCell;
use std::collections::HashMap;
use std::sync::{Arc, LazyLock, Mutex};
use std::time::Duration;
use sysinfo::{MemoryRefreshKind, Pid, ProcessesToUpdate, RefreshKind, System};
use tokio::runtime::{<PERSON>uild<PERSON>, Handle, Runtime};


static SYSTEM: LazyLock<Arc<Mutex<System>>> = LazyLock::new(|| {
    let system = System::new_all();
    Arc::new(Mutex::new(system))
});

static PID: LazyLock<Pid> = LazyLock::new(|| Pid::from_u32(std::process::id()));

fn main() {
    init_log();
    print_mem();

    // RT.spawn( async move {
    //     // win实时观测内存使用情况
    //     loop {
    //         // 获取总内存和可用内存
    //         let total_memory = system.total_memory();
    //         let free_memory = system.free_memory();
    //
    //         info!("Total Memory: {} KB", total_memory);
    //         info!("Free Memory: {} KB", free_memory);
    //         tokio::time::sleep(Duration::from_secs(1)).await;
    //     }
    // });
    if let Ok(rt) = RT3.read() {
        rt.block_on(async {
            let mut tasks = vec![];
            for i in 0..3 {
                tasks.push(rt.spawn(async {
                    // 优化：预分配 HashMap 容量，避免频繁重新分配
                    let mut v = HashMap::with_capacity(5000000);
                    for i in 0..5000000 {
                        let uid = "dssadfsdfsadfadsfsavsavfavvffdafasddf".to_string();
                        v.insert(i, uid);
                    }
                    // tokio::time::sleep(Duration::from_secs(3)).await;
                    // print_mem();
                    // drop(v);
                    print_mem();
                    v
                }));
            }
            for task in tasks {
                let _ = task.await;
            }
        })
    }

    {
        let rt = RT4.write().unwrap();
        let mut rt3 = rt.metrics().num_alive_tasks();
        // let mut val = RT3.write().unwrap();
        // let rt = val.take().unwrap();
        // info!("{:?}", rt.metrics().num_alive_tasks());
        // // rt.shutdown_timeout(Duration::from_secs(1));
        // // (*RT3).shutdown_timeout(Duration::from_secs(4));
        // // rt.shutdown_timeout(Duration::from_secs(4));
        // info!("{:?}", val);
        // drop(val);
        // info!("{:?}", RT3.read().unwrap());
        print_mem();
    }
}

fn print_mem() {
    // 添加错误处理机制
    match SYSTEM.lock() {
        Ok(mut sys) => {
            sys.refresh_memory();
            sys.refresh_processes(ProcessesToUpdate::Some(&[*PID]), true);
            
            match sys.process(*PID) {
                Some(process) => {
                    let vm = process.virtual_memory() / 1024;
                    let m = process.memory() / 1024;
                    
                    // 添加运行时任务数量监控
                    match RT3.read() {
                        Ok(rt) => {
                            info!("活跃任务数: {:?}", rt.metrics().num_alive_tasks());
                        }
                        Err(e) => {
                            log::warn!("无法获取运行时指标: {:?}", e);
                        }
                    }
                    
                    info!("内存使用量: {} KB", m);
                    info!("虚拟内存: {} KB", vm);
                }
                None => {
                    log::warn!("无法找到当前进程信息 (PID: {:?})", *PID);
                }
            }
        }
        Err(e) => {
            log::error!("无法获取系统锁: {:?}", e);
        }
    }
}
