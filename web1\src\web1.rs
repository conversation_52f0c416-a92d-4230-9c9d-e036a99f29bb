use crate::router::routers::init_route;
use entity::add;
use log::info;
use mycore::config::db::init_db;
use mycore::config::log::init_log;
use mycore::config::rt::{RT, RT2};
use mycore::config::state::AppState;
use std::fs::File;
use std::sync::Arc;
use std::thread;
use std::time::Duration;
use tokio::net::TcpListener;

pub fn start_web(addr: &str) {
    // console_subscriber::init();
    flame::start("main");
    init_log();
    let state: Arc<AppState> = init_db();
    (*RT2).spawn(async move {
        info!("start");
        // info!("wechat: {}", wechat::weapp::Auth::get_session_key("wxf44661bfaf114fb2", "71dfc4fe336542a78a4320232d37dc3d", "0f1MahGa13GAwJ08BlGa1cTwqP3MahG6").await.unwrap());
        // let client = reqwest::Client::new();
        // let resp = client.get("https://www.baidu.com").send().await.unwrap();
        // info!("{:?}", resp);
        // thread::sleep(Duration::from_secs(10));
        // flame::end("main");
        // flame::dump_html(File::create("test.html").unwrap()).unwrap();
    });

    let _ = (*RT).block_on(async {
        let app = init_route(state).await;
        let listener = TcpListener::bind(addr).await.unwrap();

        let _ = axum::serve(listener, app).await;
    });
}
