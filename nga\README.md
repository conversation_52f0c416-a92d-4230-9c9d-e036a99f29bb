# NGA 论坛数据爬虫

优化版本的 NGA 论坛数据爬虫，支持 Bark 推送通知功能。

## 功能特性

- ✅ **数据抓取**：支持主题、帖子、回复数据抓取
- ✅ **Bark 推送**：实时推送新回复通知到 iOS 设备
- ✅ **配置管理**：通过环境变量管理所有配置
- ✅ **错误处理**：完善的异常处理和日志记录
- ✅ **数据库支持**：PostgreSQL 数据存储
- ✅ **类型注解**：完整的 Python 类型提示

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置环境变量

复制 `env.example` 为 `.env` 并填写配置：

```bash
cp env.example .env
# 编辑 .env 文件，填写你的配置
```

### 3. 运行爬虫

```bash
# 爬取回复数据（默认模式）
python -m nga.nga

# 爬取主题数据
python -m nga.nga -m subject

# 爬取指定主题的帖子
python -m nga.nga -m post -t 12345
```

## 配置说明

### 数据库配置
- `DATABASE_URL`: PostgreSQL 连接字符串

### NGA 配置
- `NGA_COOKIE`: NGA 论坛 Cookie（必需）

### Bark 推送配置
- `BARK_ENABLED`: 是否启用 Bark 推送（true/false）
- `BARK_SERVER_URL`: Bark 服务器地址
- `BARK_DEVICE_KEY`: Bark 设备密钥
- `BARK_DEFAULT_PRIORITY`: 默认推送优先级（0-10）

### 日志配置
- `LOG_LEVEL`: 日志级别（DEBUG/INFO/WARNING/ERROR）
- `LOG_FILE`: 日志文件路径

## Bark 推送设置

1. 在 iOS 设备上安装 [Bark](https://apps.apple.com/us/app/bark-customed-notifications/id1403753865) 应用
2. 获取设备密钥
3. 在 `.env` 文件中配置 Bark 相关参数

## 代码结构

```
nga/
├── __init__.py          # 包初始化
├── config.py            # 配置管理
├── notifier.py          # Bark 推送模块
├── nga.py               # 主爬虫逻辑
├── requirements.txt     # 依赖包清单
├── env.example          # 配置示例
└── README.md           # 项目说明
```

## 更新日志

### v2.0.0
- 重构代码架构，提高可维护性
- 添加 Bark 推送通知功能
- 完善错误处理和日志记录
- 添加配置管理模块
- 支持环境变量配置
- 添加类型注解

### v1.0.0
- 基础 NGA 数据抓取功能

## 注意事项

- 请遵守 NGA 论坛的使用条款
- 建议设置合理的请求间隔，避免过于频繁的访问
- 妥善保管 Cookie 等敏感信息
- 定期更新 Cookie 以确保正常访问 