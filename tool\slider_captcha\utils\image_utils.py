"""
图像处理工具类

提供滑块验证码处理所需的各种图像操作功能：
- 图像下载和格式转换
- 坐标系变换
- 图像预处理
- 调试图像保存
"""

import os
import base64
import logging
from io import BytesIO
from typing import Tuple, Optional, Union, Dict
from pathlib import Path

import numpy as np
import requests
from PIL import Image


class ImageUtils:
    """图像处理工具类"""
    
    def __init__(self, config_manager=None):
        """
        初始化图像工具
        
        Args:
            config_manager: 配置管理器实例
        """
        self.config_manager = config_manager
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 从配置获取调试设置
        if config_manager:
            self.debug_mode = config_manager.get('captcha.debug_mode', False)
            self.save_debug_images = config_manager.get('captcha.save_debug_images', False)
        else:
            self.debug_mode = False
            self.save_debug_images = False
    
    def download_image_from_url(self, url: str, timeout: int = 10) -> np.ndarray:
        """
        从URL下载图像并转换为numpy数组
        
        Args:
            url: 图像URL
            timeout: 请求超时时间（秒）
            
        Returns:
            np.ndarray: BGR格式的图像数组
            
        Raises:
            requests.RequestException: 下载失败
            ValueError: 图像格式无效
        """
        try:
            self.logger.debug(f"正在下载图像: {url}")
            
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            
            response = requests.get(url, headers=headers, timeout=timeout)
            response.raise_for_status()
            
            # 转换为PIL图像
            pil_image = Image.open(BytesIO(response.content))
            
            # 转换为BGR格式的numpy数组（OpenCV格式）
            if pil_image.mode != 'RGB':
                pil_image = pil_image.convert('RGB')
                
            numpy_image = np.array(pil_image)
            bgr_image = numpy_image[:, :, ::-1]  # RGB转BGR
            
            self.logger.debug(f"图像下载成功，尺寸: {bgr_image.shape}")
            return bgr_image
            
        except Exception as e:
            self.logger.error(f"图像下载失败: {e}")
            raise
    
    def base64_to_image(self, base64_str: str) -> np.ndarray:
        """
        将base64字符串转换为图像数组
        
        Args:
            base64_str: base64编码的图像字符串
            
        Returns:
            np.ndarray: BGR格式的图像数组
        """
        try:
            # 移除data:image前缀（如果存在）
            if base64_str.startswith('data:image'):
                base64_str = base64_str.split(',', 1)[1]
            
            # 解码base64
            image_data = base64.b64decode(base64_str)
            
            # 转换为PIL图像
            pil_image = Image.open(BytesIO(image_data))
            
            # 转换为BGR格式
            if pil_image.mode != 'RGB':
                pil_image = pil_image.convert('RGB')
                
            numpy_image = np.array(pil_image)
            bgr_image = numpy_image[:, :, ::-1]  # RGB转BGR
            
            self.logger.debug(f"base64图像转换成功，尺寸: {bgr_image.shape}")
            return bgr_image
            
        except Exception as e:
            self.logger.error(f"base64图像转换失败: {e}")
            raise ValueError(f"无效的base64图像数据: {e}")
    
    def pil_to_opencv(self, pil_image: Image.Image) -> np.ndarray:
        """
        将PIL图像转换为OpenCV格式
        
        Args:
            pil_image: PIL图像对象
            
        Returns:
            np.ndarray: BGR格式的图像数组
        """
        # 确保是RGB格式
        if pil_image.mode != 'RGB':
            pil_image = pil_image.convert('RGB')
        
        # 转换为numpy数组并转换颜色通道
        numpy_image = np.array(pil_image)
        bgr_image = numpy_image[:, :, ::-1]  # RGB转BGR
        
        return bgr_image
    
    def opencv_to_pil(self, opencv_image: np.ndarray) -> Image.Image:
        """
        将OpenCV图像转换为PIL格式
        
        Args:
            opencv_image: BGR格式的OpenCV图像数组
            
        Returns:
            Image.Image: PIL图像对象
        """
        # BGR转RGB
        rgb_image = opencv_image[:, :, ::-1]
        return Image.fromarray(rgb_image)
    
    def resize_image(self, image: np.ndarray, 
                    target_size: Optional[Tuple[int, int]] = None,
                    scale_factor: Optional[float] = None) -> Tuple[np.ndarray, float]:
        """
        调整图像尺寸
        
        Args:
            image: 输入图像
            target_size: 目标尺寸(width, height)，与scale_factor二选一
            scale_factor: 缩放因子，与target_size二选一
            
        Returns:
            Tuple[np.ndarray, float]: (调整后的图像, 实际缩放因子)
        """
        try:
            import cv2
        except ImportError:
            raise ImportError("请安装OpenCV: pip install opencv-python")
        
        original_height, original_width = image.shape[:2]
        
        if target_size is not None:
            target_width, target_height = target_size
            scale_x = target_width / original_width
            scale_y = target_height / original_height
            actual_scale = min(scale_x, scale_y)  # 保持比例
        elif scale_factor is not None:
            actual_scale = scale_factor
            target_width = int(original_width * scale_factor)
            target_height = int(original_height * scale_factor)
        else:
            raise ValueError("必须指定target_size或scale_factor中的一个")
        
        resized_image = cv2.resize(image, (target_width, target_height), 
                                 interpolation=cv2.INTER_LINEAR)
        
        self.logger.debug(f"图像尺寸调整: {original_width}x{original_height} -> {target_width}x{target_height}, 缩放因子: {actual_scale:.3f}")
        
        return resized_image, actual_scale
    
    def scale_coordinates(self, coordinates: Tuple[int, int], 
                         scale_factor: float, 
                         reverse: bool = False) -> Tuple[int, int]:
        """
        缩放坐标
        
        Args:
            coordinates: 原始坐标(x, y)
            scale_factor: 缩放因子
            reverse: 是否反向缩放（从缩放后坐标转换为原始坐标）
            
        Returns:
            Tuple[int, int]: 缩放后的坐标
        """
        x, y = coordinates
        
        if reverse:
            # 反向缩放：缩放后坐标 -> 原始坐标
            scaled_x = int(x / scale_factor)
            scaled_y = int(y / scale_factor)
        else:
            # 正向缩放：原始坐标 -> 缩放后坐标
            scaled_x = int(x * scale_factor)
            scaled_y = int(y * scale_factor)
        
        return scaled_x, scaled_y
    
    def crop_image(self, image: np.ndarray, 
                  x: int, y: int, width: int, height: int) -> np.ndarray:
        """
        裁剪图像
        
        Args:
            image: 输入图像
            x, y: 裁剪区域左上角坐标
            width, height: 裁剪区域宽度和高度
            
        Returns:
            np.ndarray: 裁剪后的图像
        """
        img_height, img_width = image.shape[:2]
        
        # 边界检查
        x = max(0, min(x, img_width - 1))
        y = max(0, min(y, img_height - 1))
        x2 = min(x + width, img_width)
        y2 = min(y + height, img_height)
        
        cropped = image[y:y2, x:x2]
        self.logger.debug(f"图像裁剪: ({x},{y}) 尺寸{width}x{height}")
        
        return cropped
    
    def preprocess_for_detection(self, image: np.ndarray) -> np.ndarray:
        """
        为缺口检测预处理图像
        
        Args:
            image: 输入图像
            
        Returns:
            np.ndarray: 预处理后的图像
        """
        try:
            import cv2
        except ImportError:
            raise ImportError("请安装OpenCV: pip install opencv-python")
        
        # 转换为灰度图
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image
        
        # 高斯模糊去噪
        if self.config_manager:
            kernel_size = self.config_manager.get('opencv.gaussian_blur_kernel', (5, 5))
        else:
            kernel_size = (5, 5)
            
        blurred = cv2.GaussianBlur(gray, kernel_size, 0)
        
        self.logger.debug("图像预处理完成：转换为灰度图并应用高斯模糊")
        return blurred
    
    def save_debug_image(self, image: np.ndarray, 
                        filename: str, 
                        subdir: str = "debug") -> None:
        """
        保存调试图像
        
        Args:
            image: 要保存的图像
            filename: 文件名
            subdir: 子目录名
        """
        if not self.save_debug_images:
            return
        
        try:
            # 创建调试目录
            debug_dir = Path("tool/slider_captcha/debug") / subdir
            debug_dir.mkdir(parents=True, exist_ok=True)
            
            # 保存图像
            save_path = debug_dir / filename
            
            # 使用PIL保存（支持更多格式）
            if len(image.shape) == 3:
                pil_image = self.opencv_to_pil(image)
            else:
                pil_image = Image.fromarray(image)
            
            pil_image.save(save_path)
            self.logger.debug(f"调试图像已保存: {save_path}")
            
        except Exception as e:
            self.logger.warning(f"保存调试图像失败: {e}")
    
    def create_visualization(self, image: np.ndarray, 
                           gap_position: Optional[Tuple[int, int]] = None,
                           trajectory: Optional[list] = None,
                           title: str = "验证码分析") -> np.ndarray:
        """
        创建可视化图像，标注缺口位置和轨迹
        
        Args:
            image: 原始图像
            gap_position: 缺口位置(x, y)
            trajectory: 轨迹点列表
            title: 图像标题
            
        Returns:
            np.ndarray: 带标注的图像
        """
        try:
            import cv2
        except ImportError:
            raise ImportError("请安装OpenCV: pip install opencv-python")
        
        # 复制图像以避免修改原图
        vis_image = image.copy()
        
        # 标注缺口位置
        if gap_position:
            x, y = gap_position
            cv2.circle(vis_image, (x, y), 10, (0, 255, 0), 2)  # 绿色圆圈
            cv2.putText(vis_image, f"缺口({x},{y})", (x+15, y-15), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
        
        # 绘制轨迹
        if trajectory:
            start_x = gap_position[0] if gap_position else 0
            current_x = start_x
            
            for i, (displacement, _) in enumerate(trajectory):
                next_x = current_x + displacement
                # 绘制轨迹线
                cv2.line(vis_image, (current_x, 50), (next_x, 50), (255, 0, 0), 2)
                current_x = next_x
        
        # 添加标题
        cv2.putText(vis_image, title, (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        
        return vis_image
    
    def get_image_info(self, image: np.ndarray) -> Dict[str, any]:
        """
        获取图像信息
        
        Args:
            image: 输入图像
            
        Returns:
            Dict[str, any]: 图像信息字典
        """
        info = {
            'shape': image.shape,
            'dtype': str(image.dtype),
            'size': image.size,
            'dimensions': len(image.shape)
        }
        
        if len(image.shape) == 3:
            info['channels'] = image.shape[2]
            info['color_space'] = 'BGR' if image.shape[2] == 3 else 'BGRA'
        else:
            info['channels'] = 1
            info['color_space'] = 'Grayscale'
        
        return info
    
    def __str__(self) -> str:
        """返回图像工具的字符串表示"""
        return f"ImageUtils(debug_mode={self.debug_mode}, save_debug_images={self.save_debug_images})" 