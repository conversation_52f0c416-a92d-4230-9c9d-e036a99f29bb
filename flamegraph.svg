<?xml version="1.0" standalone="no"?><!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd"><svg version="1.1" width="1200" height="566" onload="init(evt)" viewBox="0 0 1200 566" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:fg="http://github.com/jonhoo/inferno"><!--Flame graph stack visualization. See https://github.com/brendangregg/FlameGraph for latest version, and http://www.brendangregg.com/flamegraphs.html for examples.--><!--NOTES: --><defs><linearGradient id="background" y1="0" y2="1" x1="0" x2="0"><stop stop-color="#eeeeee" offset="5%"/><stop stop-color="#eeeeb0" offset="95%"/></linearGradient></defs><style type="text/css">
text { font-family:monospace; font-size:12px }
#title { text-anchor:middle; font-size:17px; }
#matched { text-anchor:end; }
#search { text-anchor:end; opacity:0.1; cursor:pointer; }
#search:hover, #search.show { opacity:1; }
#subtitle { text-anchor:middle; font-color:rgb(160,160,160); }
#unzoom { cursor:pointer; }
#frames > *:hover { stroke:black; stroke-width:0.5; cursor:pointer; }
.hide { display:none; }
.parent { opacity:0.5; }
</style><script type="text/ecmascript"><![CDATA[
        var nametype = 'Function:';
        var fontsize = 12;
        var fontwidth = 0.59;
        var xpad = 10;
        var inverted = false;
        var searchcolor = 'rgb(230,0,230)';
        var fluiddrawing = true;
        var truncate_text_right = false;
    ]]><![CDATA["use strict";
var details, searchbtn, unzoombtn, matchedtxt, svg, searching, frames, known_font_width;
function init(evt) {
    details = document.getElementById("details").firstChild;
    searchbtn = document.getElementById("search");
    unzoombtn = document.getElementById("unzoom");
    matchedtxt = document.getElementById("matched");
    svg = document.getElementsByTagName("svg")[0];
    frames = document.getElementById("frames");
    known_font_width = get_monospace_width(frames);
    total_samples = parseInt(frames.attributes.total_samples.value);
    searching = 0;

    // Use GET parameters to restore a flamegraph's state.
    var restore_state = function() {
        var params = get_params();
        if (params.x && params.y)
            zoom(find_group(document.querySelector('[*|x="' + params.x + '"][y="' + params.y + '"]')));
        if (params.s)
            search(params.s);
    };

    if (fluiddrawing) {
        // Make width dynamic so the SVG fits its parent's width.
        svg.removeAttribute("width");
        // Edge requires us to have a viewBox that gets updated with size changes.
        var isEdge = /Edge\/\d./i.test(navigator.userAgent);
        if (!isEdge) {
            svg.removeAttribute("viewBox");
        }
        var update_for_width_change = function() {
            if (isEdge) {
                svg.attributes.viewBox.value = "0 0 " + svg.width.baseVal.value + " " + svg.height.baseVal.value;
            }

            // Keep consistent padding on left and right of frames container.
            frames.attributes.width.value = svg.width.baseVal.value - xpad * 2;

            // Text truncation needs to be adjusted for the current width.
            update_text_for_elements(frames.children);

            // Keep search elements at a fixed distance from right edge.
            var svgWidth = svg.width.baseVal.value;
            searchbtn.attributes.x.value = svgWidth - xpad;
            matchedtxt.attributes.x.value = svgWidth - xpad;
        };
        window.addEventListener('resize', function() {
            update_for_width_change();
        });
        // This needs to be done asynchronously for Safari to work.
        setTimeout(function() {
            unzoom();
            update_for_width_change();
            restore_state();
        }, 0);
    } else {
        restore_state();
    }
}
// event listeners
window.addEventListener("click", function(e) {
    var target = find_group(e.target);
    if (target) {
        if (target.nodeName == "a") {
            if (e.ctrlKey === false) return;
            e.preventDefault();
        }
        if (target.classList.contains("parent")) unzoom();
        zoom(target);

        // set parameters for zoom state
        var el = target.querySelector("rect");
        if (el && el.attributes && el.attributes.y && el.attributes["fg:x"]) {
            var params = get_params()
            params.x = el.attributes["fg:x"].value;
            params.y = el.attributes.y.value;
            history.replaceState(null, null, parse_params(params));
        }
    }
    else if (e.target.id == "unzoom") {
        unzoom();

        // remove zoom state
        var params = get_params();
        if (params.x) delete params.x;
        if (params.y) delete params.y;
        history.replaceState(null, null, parse_params(params));
    }
    else if (e.target.id == "search") search_prompt();
}, false)
// mouse-over for info
// show
window.addEventListener("mouseover", function(e) {
    var target = find_group(e.target);
    if (target) details.nodeValue = nametype + " " + g_to_text(target);
}, false)
// clear
window.addEventListener("mouseout", function(e) {
    var target = find_group(e.target);
    if (target) details.nodeValue = ' ';
}, false)
// ctrl-F for search
window.addEventListener("keydown",function (e) {
    if (e.keyCode === 114 || (e.ctrlKey && e.keyCode === 70)) {
        e.preventDefault();
        search_prompt();
    }
}, false)
// functions
function get_params() {
    var params = {};
    var paramsarr = window.location.search.substr(1).split('&');
    for (var i = 0; i < paramsarr.length; ++i) {
        var tmp = paramsarr[i].split("=");
        if (!tmp[0] || !tmp[1]) continue;
        params[tmp[0]]  = decodeURIComponent(tmp[1]);
    }
    return params;
}
function parse_params(params) {
    var uri = "?";
    for (var key in params) {
        uri += key + '=' + encodeURIComponent(params[key]) + '&';
    }
    if (uri.slice(-1) == "&")
        uri = uri.substring(0, uri.length - 1);
    if (uri == '?')
        uri = window.location.href.split('?')[0];
    return uri;
}
function find_child(node, selector) {
    var children = node.querySelectorAll(selector);
    if (children.length) return children[0];
    return;
}
function find_group(node) {
    var parent = node.parentElement;
    if (!parent) return;
    if (parent.id == "frames") return node;
    return find_group(parent);
}
function orig_save(e, attr, val) {
    if (e.attributes["fg:orig_" + attr] != undefined) return;
    if (e.attributes[attr] == undefined) return;
    if (val == undefined) val = e.attributes[attr].value;
    e.setAttribute("fg:orig_" + attr, val);
}
function orig_load(e, attr) {
    if (e.attributes["fg:orig_"+attr] == undefined) return;
    e.attributes[attr].value = e.attributes["fg:orig_" + attr].value;
    e.removeAttribute("fg:orig_" + attr);
}
function g_to_text(e) {
    var text = find_child(e, "title").firstChild.nodeValue;
    return (text)
}
function g_to_func(e) {
    var func = g_to_text(e);
    // if there's any manipulation we want to do to the function
    // name before it's searched, do it here before returning.
    return (func);
}
function get_monospace_width(frames) {
    // Given the id="frames" element, return the width of text characters if
    // this is a monospace font, otherwise return 0.
    text = find_child(frames.children[0], "text");
    originalContent = text.textContent;
    text.textContent = "!";
    bangWidth = text.getComputedTextLength();
    text.textContent = "W";
    wWidth = text.getComputedTextLength();
    text.textContent = originalContent;
    if (bangWidth === wWidth) {
        return bangWidth;
    } else {
        return 0;
    }
}
function update_text_for_elements(elements) {
    // In order to render quickly in the browser, you want to do one pass of
    // reading attributes, and one pass of mutating attributes. See
    // https://web.dev/avoid-large-complex-layouts-and-layout-thrashing/ for details.

    // Fall back to inefficient calculation, if we're variable-width font.
    // TODO This should be optimized somehow too.
    if (known_font_width === 0) {
        for (var i = 0; i < elements.length; i++) {
            update_text(elements[i]);
        }
        return;
    }

    var textElemNewAttributes = [];
    for (var i = 0; i < elements.length; i++) {
        var e = elements[i];
        var r = find_child(e, "rect");
        var t = find_child(e, "text");
        var w = parseFloat(r.attributes.width.value) * frames.attributes.width.value / 100 - 3;
        var txt = find_child(e, "title").textContent.replace(/\([^(]*\)$/,"");
        var newX = format_percent((parseFloat(r.attributes.x.value) + (100 * 3 / frames.attributes.width.value)));

        // Smaller than this size won't fit anything
        if (w < 2 * known_font_width) {
            textElemNewAttributes.push([newX, ""]);
            continue;
        }

        // Fit in full text width
        if (txt.length * known_font_width < w) {
            textElemNewAttributes.push([newX, txt]);
            continue;
        }

        var substringLength = Math.floor(w / known_font_width) - 2;
        if (truncate_text_right) {
            // Truncate the right side of the text.
            textElemNewAttributes.push([newX, txt.substring(0, substringLength) + ".."]);
            continue;
        } else {
            // Truncate the left side of the text.
            textElemNewAttributes.push([newX, ".." + txt.substring(txt.length - substringLength, txt.length)]);
            continue;
        }
    }

    console.assert(textElemNewAttributes.length === elements.length, "Resize failed, please file a bug at https://github.com/jonhoo/inferno/");

    // Now that we know new textContent, set it all in one go so we don't refresh a bazillion times.
    for (var i = 0; i < elements.length; i++) {
        var e = elements[i];
        var values = textElemNewAttributes[i];
        var t = find_child(e, "text");
        t.attributes.x.value = values[0];
        t.textContent = values[1];
    }
}

function update_text(e) {
    var r = find_child(e, "rect");
    var t = find_child(e, "text");
    var w = parseFloat(r.attributes.width.value) * frames.attributes.width.value / 100 - 3;
    var txt = find_child(e, "title").textContent.replace(/\([^(]*\)$/,"");
    t.attributes.x.value = format_percent((parseFloat(r.attributes.x.value) + (100 * 3 / frames.attributes.width.value)));

    // Smaller than this size won't fit anything
    if (w < 2 * fontsize * fontwidth) {
        t.textContent = "";
        return;
    }
    t.textContent = txt;
    // Fit in full text width
    if (t.getComputedTextLength() < w)
        return;
    if (truncate_text_right) {
        // Truncate the right side of the text.
        for (var x = txt.length - 2; x > 0; x--) {
            if (t.getSubStringLength(0, x + 2) <= w) {
                t.textContent = txt.substring(0, x) + "..";
                return;
            }
        }
    } else {
        // Truncate the left side of the text.
        for (var x = 2; x < txt.length; x++) {
            if (t.getSubStringLength(x - 2, txt.length) <= w) {
                t.textContent = ".." + txt.substring(x, txt.length);
                return;
            }
        }
    }
    t.textContent = "";
}
// zoom
function zoom_reset(e) {
    if (e.tagName == "rect") {
        e.attributes.x.value = format_percent(100 * parseInt(e.attributes["fg:x"].value) / total_samples);
        e.attributes.width.value = format_percent(100 * parseInt(e.attributes["fg:w"].value) / total_samples);
    }
    if (e.childNodes == undefined) return;
    for(var i = 0, c = e.childNodes; i < c.length; i++) {
        zoom_reset(c[i]);
    }
}
function zoom_child(e, x, zoomed_width_samples) {
    if (e.tagName == "text") {
        var parent_x = parseFloat(find_child(e.parentNode, "rect[x]").attributes.x.value);
        e.attributes.x.value = format_percent(parent_x + (100 * 3 / frames.attributes.width.value));
    } else if (e.tagName == "rect") {
        e.attributes.x.value = format_percent(100 * (parseInt(e.attributes["fg:x"].value) - x) / zoomed_width_samples);
        e.attributes.width.value = format_percent(100 * parseInt(e.attributes["fg:w"].value) / zoomed_width_samples);
    }
    if (e.childNodes == undefined) return;
    for(var i = 0, c = e.childNodes; i < c.length; i++) {
        zoom_child(c[i], x, zoomed_width_samples);
    }
}
function zoom_parent(e) {
    if (e.attributes) {
        if (e.attributes.x != undefined) {
            e.attributes.x.value = "0.0%";
        }
        if (e.attributes.width != undefined) {
            e.attributes.width.value = "100.0%";
        }
    }
    if (e.childNodes == undefined) return;
    for(var i = 0, c = e.childNodes; i < c.length; i++) {
        zoom_parent(c[i]);
    }
}
function zoom(node) {
    var attr = find_child(node, "rect").attributes;
    var width = parseInt(attr["fg:w"].value);
    var xmin = parseInt(attr["fg:x"].value);
    var xmax = xmin + width;
    var ymin = parseFloat(attr.y.value);
    unzoombtn.classList.remove("hide");
    var el = frames.children;
    var to_update_text = [];
    for (var i = 0; i < el.length; i++) {
        var e = el[i];
        var a = find_child(e, "rect").attributes;
        var ex = parseInt(a["fg:x"].value);
        var ew = parseInt(a["fg:w"].value);
        // Is it an ancestor
        if (!inverted) {
            var upstack = parseFloat(a.y.value) > ymin;
        } else {
            var upstack = parseFloat(a.y.value) < ymin;
        }
        if (upstack) {
            // Direct ancestor
            if (ex <= xmin && (ex+ew) >= xmax) {
                e.classList.add("parent");
                zoom_parent(e);
                to_update_text.push(e);
            }
            // not in current path
            else
                e.classList.add("hide");
        }
        // Children maybe
        else {
            // no common path
            if (ex < xmin || ex >= xmax) {
                e.classList.add("hide");
            }
            else {
                zoom_child(e, xmin, width);
                to_update_text.push(e);
            }
        }
    }
    update_text_for_elements(to_update_text);
}
function unzoom() {
    unzoombtn.classList.add("hide");
    var el = frames.children;
    for(var i = 0; i < el.length; i++) {
        el[i].classList.remove("parent");
        el[i].classList.remove("hide");
        zoom_reset(el[i]);
    }
    update_text_for_elements(el);
}
// search
function reset_search() {
    var el = document.querySelectorAll("#frames rect");
    for (var i = 0; i < el.length; i++) {
        orig_load(el[i], "fill")
    }
    var params = get_params();
    delete params.s;
    history.replaceState(null, null, parse_params(params));
}
function search_prompt() {
    if (!searching) {
        var term = prompt("Enter a search term (regexp " +
            "allowed, eg: ^ext4_)", "");
        if (term != null) {
            search(term)
        }
    } else {
        reset_search();
        searching = 0;
        searchbtn.classList.remove("show");
        searchbtn.firstChild.nodeValue = "Search"
        matchedtxt.classList.add("hide");
        matchedtxt.firstChild.nodeValue = ""
    }
}
function search(term) {
    var re = new RegExp(term);
    var el = frames.children;
    var matches = new Object();
    var maxwidth = 0;
    for (var i = 0; i < el.length; i++) {
        var e = el[i];
        // Skip over frames which are either not visible, or below the zoomed-to frame
        if (e.classList.contains("hide") || e.classList.contains("parent")) {
            continue;
        }
        var func = g_to_func(e);
        var rect = find_child(e, "rect");
        if (func == null || rect == null)
            continue;
        // Save max width. Only works as we have a root frame
        var w = parseInt(rect.attributes["fg:w"].value);
        if (w > maxwidth)
            maxwidth = w;
        if (func.match(re)) {
            // highlight
            var x = parseInt(rect.attributes["fg:x"].value);
            orig_save(rect, "fill");
            rect.attributes.fill.value = searchcolor;
            // remember matches
            if (matches[x] == undefined) {
                matches[x] = w;
            } else {
                if (w > matches[x]) {
                    // overwrite with parent
                    matches[x] = w;
                }
            }
            searching = 1;
        }
    }
    if (!searching)
        return;
    var params = get_params();
    params.s = term;
    history.replaceState(null, null, parse_params(params));

    searchbtn.classList.add("show");
    searchbtn.firstChild.nodeValue = "Reset Search";
    // calculate percent matched, excluding vertical overlap
    var count = 0;
    var lastx = -1;
    var lastw = 0;
    var keys = Array();
    for (k in matches) {
        if (matches.hasOwnProperty(k))
            keys.push(k);
    }
    // sort the matched frames by their x location
    // ascending, then width descending
    keys.sort(function(a, b){
        return a - b;
    });
    // Step through frames saving only the biggest bottom-up frames
    // thanks to the sort order. This relies on the tree property
    // where children are always smaller than their parents.
    for (var k in keys) {
        var x = parseInt(keys[k]);
        var w = matches[keys[k]];
        if (x >= lastx + lastw) {
            count += w;
            lastx = x;
            lastw = w;
        }
    }
    // display matched percent
    matchedtxt.classList.remove("hide");
    var pct = 100 * count / maxwidth;
    if (pct != 100) pct = pct.toFixed(1);
    matchedtxt.firstChild.nodeValue = "Matched: " + pct + "%";
}
function format_percent(n) {
    return n.toFixed(4) + "%";
}
]]></script><rect x="0" y="0" width="100%" height="566" fill="url(#background)"/><text id="title" fill="rgb(0,0,0)" x="50.0000%" y="24.00">Flame Graph</text><text id="details" fill="rgb(0,0,0)" x="10" y="549.00"> </text><text id="unzoom" class="hide" fill="rgb(0,0,0)" x="10" y="24.00">Reset Zoom</text><text id="search" fill="rgb(0,0,0)" x="1190" y="24.00">Search</text><text id="matched" fill="rgb(0,0,0)" x="1190" y="549.00"> </text><svg id="frames" x="10" width="1180" total_samples="23869899"><g><title>__irqentry_text_end (4,981,380 samples, 20.87%)</title><rect x="18.2775%" y="405" width="20.8689%" height="15" fill="rgb(227,0,7)" fg:x="4362830" fg:w="4981380"/><text x="18.5275%" y="415.50">__irqentry_text_end</text></g><g><title>filemap_alloc_folio (4,834,356 samples, 20.25%)</title><rect x="39.1464%" y="213" width="20.2529%" height="15" fill="rgb(217,0,24)" fg:x="9344210" fg:w="4834356"/><text x="39.3964%" y="223.50">filemap_alloc_folio</text></g><g><title>folio_alloc (4,834,356 samples, 20.25%)</title><rect x="39.1464%" y="197" width="20.2529%" height="15" fill="rgb(221,193,54)" fg:x="9344210" fg:w="4834356"/><text x="39.3964%" y="207.50">folio_alloc</text></g><g><title>alloc_pages (4,834,356 samples, 20.25%)</title><rect x="39.1464%" y="181" width="20.2529%" height="15" fill="rgb(248,212,6)" fg:x="9344210" fg:w="4834356"/><text x="39.3964%" y="191.50">alloc_pages</text></g><g><title>__alloc_pages (4,834,356 samples, 20.25%)</title><rect x="39.1464%" y="165" width="20.2529%" height="15" fill="rgb(208,68,35)" fg:x="9344210" fg:w="4834356"/><text x="39.3964%" y="175.50">__alloc_pages</text></g><g><title>get_page_from_freelist (4,834,356 samples, 20.25%)</title><rect x="39.1464%" y="149" width="20.2529%" height="15" fill="rgb(232,128,0)" fg:x="9344210" fg:w="4834356"/><text x="39.3964%" y="159.50">get_page_from_freelist</text></g><g><title>__rmqueue_pcplist (4,834,356 samples, 20.25%)</title><rect x="39.1464%" y="133" width="20.2529%" height="15" fill="rgb(207,160,47)" fg:x="9344210" fg:w="4834356"/><text x="39.3964%" y="143.50">__rmqueue_pcplist</text></g><g><title>__list_del_entry_valid_or_report (4,834,356 samples, 20.25%)</title><rect x="39.1464%" y="117" width="20.2529%" height="15" fill="rgb(228,23,34)" fg:x="9344210" fg:w="4834356"/><text x="39.3964%" y="127.50">__list_del_entry_valid_or_report</text></g><g><title>[ld-linux-x86-64.so.2] (18,126,512 samples, 75.94%)</title><rect x="0.0000%" y="485" width="75.9388%" height="15" fill="rgb(218,30,26)" fg:x="0" fg:w="18126512"/><text x="0.2500%" y="495.50">[ld-linux-x86-64.so.2]</text></g><g><title>[ld-linux-x86-64.so.2] (18,126,512 samples, 75.94%)</title><rect x="0.0000%" y="469" width="75.9388%" height="15" fill="rgb(220,122,19)" fg:x="0" fg:w="18126512"/><text x="0.2500%" y="479.50">[ld-linux-x86-64.so.2]</text></g><g><title>[ld-linux-x86-64.so.2] (18,126,512 samples, 75.94%)</title><rect x="0.0000%" y="453" width="75.9388%" height="15" fill="rgb(250,228,42)" fg:x="0" fg:w="18126512"/><text x="0.2500%" y="463.50">[ld-linux-x86-64.so.2]</text></g><g><title>[ld-linux-x86-64.so.2] (18,126,512 samples, 75.94%)</title><rect x="0.0000%" y="437" width="75.9388%" height="15" fill="rgb(240,193,28)" fg:x="0" fg:w="18126512"/><text x="0.2500%" y="447.50">[ld-linux-x86-64.so.2]</text></g><g><title>[ld-linux-x86-64.so.2] (18,126,512 samples, 75.94%)</title><rect x="0.0000%" y="421" width="75.9388%" height="15" fill="rgb(216,20,37)" fg:x="0" fg:w="18126512"/><text x="0.2500%" y="431.50">[ld-linux-x86-64.so.2]</text></g><g><title>asm_exc_page_fault (8,782,302 samples, 36.79%)</title><rect x="39.1464%" y="405" width="36.7924%" height="15" fill="rgb(206,188,39)" fg:x="9344210" fg:w="8782302"/><text x="39.3964%" y="415.50">asm_exc_page_fault</text></g><g><title>exc_page_fault (8,782,302 samples, 36.79%)</title><rect x="39.1464%" y="389" width="36.7924%" height="15" fill="rgb(217,207,13)" fg:x="9344210" fg:w="8782302"/><text x="39.3964%" y="399.50">exc_page_fault</text></g><g><title>do_user_addr_fault (8,782,302 samples, 36.79%)</title><rect x="39.1464%" y="373" width="36.7924%" height="15" fill="rgb(231,73,38)" fg:x="9344210" fg:w="8782302"/><text x="39.3964%" y="383.50">do_user_addr_fault</text></g><g><title>handle_mm_fault (8,782,302 samples, 36.79%)</title><rect x="39.1464%" y="357" width="36.7924%" height="15" fill="rgb(225,20,46)" fg:x="9344210" fg:w="8782302"/><text x="39.3964%" y="367.50">handle_mm_fault</text></g><g><title>__handle_mm_fault (8,782,302 samples, 36.79%)</title><rect x="39.1464%" y="341" width="36.7924%" height="15" fill="rgb(210,31,41)" fg:x="9344210" fg:w="8782302"/><text x="39.3964%" y="351.50">__handle_mm_fault</text></g><g><title>do_fault (8,782,302 samples, 36.79%)</title><rect x="39.1464%" y="325" width="36.7924%" height="15" fill="rgb(221,200,47)" fg:x="9344210" fg:w="8782302"/><text x="39.3964%" y="335.50">do_fault</text></g><g><title>__do_fault (8,782,302 samples, 36.79%)</title><rect x="39.1464%" y="309" width="36.7924%" height="15" fill="rgb(226,26,5)" fg:x="9344210" fg:w="8782302"/><text x="39.3964%" y="319.50">__do_fault</text></g><g><title>filemap_fault (8,782,302 samples, 36.79%)</title><rect x="39.1464%" y="293" width="36.7924%" height="15" fill="rgb(249,33,26)" fg:x="9344210" fg:w="8782302"/><text x="39.3964%" y="303.50">filemap_fault</text></g><g><title>page_cache_async_ra (8,782,302 samples, 36.79%)</title><rect x="39.1464%" y="277" width="36.7924%" height="15" fill="rgb(235,183,28)" fg:x="9344210" fg:w="8782302"/><text x="39.3964%" y="287.50">page_cache_async_ra</text></g><g><title>ondemand_readahead (8,782,302 samples, 36.79%)</title><rect x="39.1464%" y="261" width="36.7924%" height="15" fill="rgb(221,5,38)" fg:x="9344210" fg:w="8782302"/><text x="39.3964%" y="271.50">ondemand_readahead</text></g><g><title>page_cache_ra_order (8,782,302 samples, 36.79%)</title><rect x="39.1464%" y="245" width="36.7924%" height="15" fill="rgb(247,18,42)" fg:x="9344210" fg:w="8782302"/><text x="39.3964%" y="255.50">page_cache_ra_order</text></g><g><title>page_cache_ra_unbounded (8,782,302 samples, 36.79%)</title><rect x="39.1464%" y="229" width="36.7924%" height="15" fill="rgb(241,131,45)" fg:x="9344210" fg:w="8782302"/><text x="39.3964%" y="239.50">page_cache_ra_unbounded</text></g><g><title>read_pages (3,947,946 samples, 16.54%)</title><rect x="59.3994%" y="213" width="16.5394%" height="15" fill="rgb(249,31,29)" fg:x="14178566" fg:w="3947946"/><text x="59.6494%" y="223.50">read_pages</text></g><g><title>netfs_readahead (3,947,946 samples, 16.54%)</title><rect x="59.3994%" y="197" width="16.5394%" height="15" fill="rgb(225,111,53)" fg:x="14178566" fg:w="3947946"/><text x="59.6494%" y="207.50">netfs_readahead</text></g><g><title>netfs_begin_read (3,947,946 samples, 16.54%)</title><rect x="59.3994%" y="181" width="16.5394%" height="15" fill="rgb(238,160,17)" fg:x="14178566" fg:w="3947946"/><text x="59.6494%" y="191.50">netfs_begin_read</text></g><g><title>v9fs_issue_read (3,947,946 samples, 16.54%)</title><rect x="59.3994%" y="165" width="16.5394%" height="15" fill="rgb(214,148,48)" fg:x="14178566" fg:w="3947946"/><text x="59.6494%" y="175.50">v9fs_issue_read</text></g><g><title>p9_client_read (3,947,946 samples, 16.54%)</title><rect x="59.3994%" y="149" width="16.5394%" height="15" fill="rgb(232,36,49)" fg:x="14178566" fg:w="3947946"/><text x="59.6494%" y="159.50">p9_client_read</text></g><g><title>p9_client_read_once (3,947,946 samples, 16.54%)</title><rect x="59.3994%" y="133" width="16.5394%" height="15" fill="rgb(209,103,24)" fg:x="14178566" fg:w="3947946"/><text x="59.6494%" y="143.50">p9_client_read_once</text></g><g><title>p9_client_rpc (3,947,946 samples, 16.54%)</title><rect x="59.3994%" y="117" width="16.5394%" height="15" fill="rgb(229,88,8)" fg:x="14178566" fg:w="3947946"/><text x="59.6494%" y="127.50">p9_client_rpc</text></g><g><title>p9_client_prepare_req (3,947,946 samples, 16.54%)</title><rect x="59.3994%" y="101" width="16.5394%" height="15" fill="rgb(213,181,19)" fg:x="14178566" fg:w="3947946"/><text x="59.6494%" y="111.50">p9_client_prepare_req</text></g><g><title>p9_tag_alloc (3,947,946 samples, 16.54%)</title><rect x="59.3994%" y="85" width="16.5394%" height="15" fill="rgb(254,191,54)" fg:x="14178566" fg:w="3947946"/><text x="59.6494%" y="95.50">p9_tag_alloc</text></g><g><title>idr_preload (3,947,946 samples, 16.54%)</title><rect x="59.3994%" y="69" width="16.5394%" height="15" fill="rgb(241,83,37)" fg:x="14178566" fg:w="3947946"/><text x="59.6494%" y="79.50">idr_preload</text></g><g><title>__radix_tree_preload (3,947,946 samples, 16.54%)</title><rect x="59.3994%" y="53" width="16.5394%" height="15" fill="rgb(233,36,39)" fg:x="14178566" fg:w="3947946"/><text x="59.6494%" y="63.50">__radix_tree_preload</text></g><g><title>preempt_count_add (3,947,946 samples, 16.54%)</title><rect x="59.3994%" y="37" width="16.5394%" height="15" fill="rgb(226,3,54)" fg:x="14178566" fg:w="3947946"/><text x="59.6494%" y="47.50">preempt_count_add</text></g><g><title>_start (3,807,776 samples, 15.95%)</title><rect x="75.9388%" y="485" width="15.9522%" height="15" fill="rgb(245,192,40)" fg:x="18126512" fg:w="3807776"/><text x="76.1888%" y="495.50">_start</text></g><g><title>__libc_start_main (3,807,776 samples, 15.95%)</title><rect x="75.9388%" y="469" width="15.9522%" height="15" fill="rgb(238,167,29)" fg:x="18126512" fg:w="3807776"/><text x="76.1888%" y="479.50">__libc_start_main</text></g><g><title>[libc.so.6] (3,807,776 samples, 15.95%)</title><rect x="75.9388%" y="453" width="15.9522%" height="15" fill="rgb(232,182,51)" fg:x="18126512" fg:w="3807776"/><text x="76.1888%" y="463.50">[libc.so.6]</text></g><g><title>main (3,807,776 samples, 15.95%)</title><rect x="75.9388%" y="437" width="15.9522%" height="15" fill="rgb(231,60,39)" fg:x="18126512" fg:w="3807776"/><text x="76.1888%" y="447.50">main</text></g><g><title>std::rt::lang_start (3,807,776 samples, 15.95%)</title><rect x="75.9388%" y="421" width="15.9522%" height="15" fill="rgb(208,69,12)" fg:x="18126512" fg:w="3807776"/><text x="76.1888%" y="431.50">std::rt::lang_start</text></g><g><title>std::rt::lang_start_internal (3,807,776 samples, 15.95%)</title><rect x="75.9388%" y="405" width="15.9522%" height="15" fill="rgb(235,93,37)" fg:x="18126512" fg:w="3807776"/><text x="76.1888%" y="415.50">std::rt::lang_start_inter..</text></g><g><title>asm_exc_page_fault (3,807,776 samples, 15.95%)</title><rect x="75.9388%" y="389" width="15.9522%" height="15" fill="rgb(213,116,39)" fg:x="18126512" fg:w="3807776"/><text x="76.1888%" y="399.50">asm_exc_page_fault</text></g><g><title>exc_page_fault (3,807,776 samples, 15.95%)</title><rect x="75.9388%" y="373" width="15.9522%" height="15" fill="rgb(222,207,29)" fg:x="18126512" fg:w="3807776"/><text x="76.1888%" y="383.50">exc_page_fault</text></g><g><title>do_user_addr_fault (3,807,776 samples, 15.95%)</title><rect x="75.9388%" y="357" width="15.9522%" height="15" fill="rgb(206,96,30)" fg:x="18126512" fg:w="3807776"/><text x="76.1888%" y="367.50">do_user_addr_fault</text></g><g><title>handle_mm_fault (3,807,776 samples, 15.95%)</title><rect x="75.9388%" y="341" width="15.9522%" height="15" fill="rgb(218,138,4)" fg:x="18126512" fg:w="3807776"/><text x="76.1888%" y="351.50">handle_mm_fault</text></g><g><title>__handle_mm_fault (3,807,776 samples, 15.95%)</title><rect x="75.9388%" y="325" width="15.9522%" height="15" fill="rgb(250,191,14)" fg:x="18126512" fg:w="3807776"/><text x="76.1888%" y="335.50">__handle_mm_fault</text></g><g><title>do_fault (3,807,776 samples, 15.95%)</title><rect x="75.9388%" y="309" width="15.9522%" height="15" fill="rgb(239,60,40)" fg:x="18126512" fg:w="3807776"/><text x="76.1888%" y="319.50">do_fault</text></g><g><title>__do_fault (3,807,776 samples, 15.95%)</title><rect x="75.9388%" y="293" width="15.9522%" height="15" fill="rgb(206,27,48)" fg:x="18126512" fg:w="3807776"/><text x="76.1888%" y="303.50">__do_fault</text></g><g><title>filemap_fault (3,807,776 samples, 15.95%)</title><rect x="75.9388%" y="277" width="15.9522%" height="15" fill="rgb(225,35,8)" fg:x="18126512" fg:w="3807776"/><text x="76.1888%" y="287.50">filemap_fault</text></g><g><title>page_cache_ra_order (3,807,776 samples, 15.95%)</title><rect x="75.9388%" y="261" width="15.9522%" height="15" fill="rgb(250,213,24)" fg:x="18126512" fg:w="3807776"/><text x="76.1888%" y="271.50">page_cache_ra_order</text></g><g><title>page_cache_ra_unbounded (3,807,776 samples, 15.95%)</title><rect x="75.9388%" y="245" width="15.9522%" height="15" fill="rgb(247,123,22)" fg:x="18126512" fg:w="3807776"/><text x="76.1888%" y="255.50">page_cache_ra_unbounded</text></g><g><title>filemap_add_folio (3,807,776 samples, 15.95%)</title><rect x="75.9388%" y="229" width="15.9522%" height="15" fill="rgb(231,138,38)" fg:x="18126512" fg:w="3807776"/><text x="76.1888%" y="239.50">filemap_add_folio</text></g><g><title>__filemap_add_folio (3,807,776 samples, 15.95%)</title><rect x="75.9388%" y="213" width="15.9522%" height="15" fill="rgb(231,145,46)" fg:x="18126512" fg:w="3807776"/><text x="76.1888%" y="223.50">__filemap_add_folio</text></g><g><title>__mod_lruvec_page_state (3,807,776 samples, 15.95%)</title><rect x="75.9388%" y="197" width="15.9522%" height="15" fill="rgb(251,118,11)" fg:x="18126512" fg:w="3807776"/><text x="76.1888%" y="207.50">__mod_lruvec_page_state</text></g><g><title>chaos-web (23,606,109 samples, 98.89%)</title><rect x="0.0000%" y="501" width="98.8949%" height="15" fill="rgb(217,147,25)" fg:x="0" fg:w="23606109"/><text x="0.2500%" y="511.50">chaos-web</text></g><g><title>entry_SYSCALL_64_after_hwframe (1,671,821 samples, 7.00%)</title><rect x="91.8910%" y="485" width="7.0039%" height="15" fill="rgb(247,81,37)" fg:x="21934288" fg:w="1671821"/><text x="92.1410%" y="495.50">entry_SYS..</text></g><g><title>do_syscall_64 (1,671,821 samples, 7.00%)</title><rect x="91.8910%" y="469" width="7.0039%" height="15" fill="rgb(209,12,38)" fg:x="21934288" fg:w="1671821"/><text x="92.1410%" y="479.50">do_syscal..</text></g><g><title>x64_sys_call (1,671,821 samples, 7.00%)</title><rect x="91.8910%" y="453" width="7.0039%" height="15" fill="rgb(227,1,9)" fg:x="21934288" fg:w="1671821"/><text x="92.1410%" y="463.50">x64_sys_c..</text></g><g><title>__x64_sys_execve (1,671,821 samples, 7.00%)</title><rect x="91.8910%" y="437" width="7.0039%" height="15" fill="rgb(248,47,43)" fg:x="21934288" fg:w="1671821"/><text x="92.1410%" y="447.50">__x64_sys..</text></g><g><title>do_execveat_common.isra.0 (1,671,821 samples, 7.00%)</title><rect x="91.8910%" y="421" width="7.0039%" height="15" fill="rgb(221,10,30)" fg:x="21934288" fg:w="1671821"/><text x="92.1410%" y="431.50">do_execve..</text></g><g><title>bprm_execve (1,671,821 samples, 7.00%)</title><rect x="91.8910%" y="405" width="7.0039%" height="15" fill="rgb(210,229,1)" fg:x="21934288" fg:w="1671821"/><text x="92.1410%" y="415.50">bprm_exec..</text></g><g><title>load_elf_binary (1,671,821 samples, 7.00%)</title><rect x="91.8910%" y="389" width="7.0039%" height="15" fill="rgb(222,148,37)" fg:x="21934288" fg:w="1671821"/><text x="92.1410%" y="399.50">load_elf_..</text></g><g><title>padzero (1,671,821 samples, 7.00%)</title><rect x="91.8910%" y="373" width="7.0039%" height="15" fill="rgb(234,67,33)" fg:x="21934288" fg:w="1671821"/><text x="92.1410%" y="383.50">padzero</text></g><g><title>asm_exc_page_fault (1,671,821 samples, 7.00%)</title><rect x="91.8910%" y="357" width="7.0039%" height="15" fill="rgb(247,98,35)" fg:x="21934288" fg:w="1671821"/><text x="92.1410%" y="367.50">asm_exc_p..</text></g><g><title>exc_page_fault (1,671,821 samples, 7.00%)</title><rect x="91.8910%" y="341" width="7.0039%" height="15" fill="rgb(247,138,52)" fg:x="21934288" fg:w="1671821"/><text x="92.1410%" y="351.50">exc_page_..</text></g><g><title>do_user_addr_fault (1,671,821 samples, 7.00%)</title><rect x="91.8910%" y="325" width="7.0039%" height="15" fill="rgb(213,79,30)" fg:x="21934288" fg:w="1671821"/><text x="92.1410%" y="335.50">do_user_a..</text></g><g><title>handle_mm_fault (1,671,821 samples, 7.00%)</title><rect x="91.8910%" y="309" width="7.0039%" height="15" fill="rgb(246,177,23)" fg:x="21934288" fg:w="1671821"/><text x="92.1410%" y="319.50">handle_mm..</text></g><g><title>__handle_mm_fault (1,671,821 samples, 7.00%)</title><rect x="91.8910%" y="293" width="7.0039%" height="15" fill="rgb(230,62,27)" fg:x="21934288" fg:w="1671821"/><text x="92.1410%" y="303.50">__handle_..</text></g><g><title>do_fault (1,671,821 samples, 7.00%)</title><rect x="91.8910%" y="277" width="7.0039%" height="15" fill="rgb(216,154,8)" fg:x="21934288" fg:w="1671821"/><text x="92.1410%" y="287.50">do_fault</text></g><g><title>__do_fault (1,671,821 samples, 7.00%)</title><rect x="91.8910%" y="261" width="7.0039%" height="15" fill="rgb(244,35,45)" fg:x="21934288" fg:w="1671821"/><text x="92.1410%" y="271.50">__do_fault</text></g><g><title>pte_alloc_one (1,671,821 samples, 7.00%)</title><rect x="91.8910%" y="245" width="7.0039%" height="15" fill="rgb(251,115,12)" fg:x="21934288" fg:w="1671821"/><text x="92.1410%" y="255.50">pte_alloc..</text></g><g><title>alloc_pages (1,671,821 samples, 7.00%)</title><rect x="91.8910%" y="229" width="7.0039%" height="15" fill="rgb(240,54,50)" fg:x="21934288" fg:w="1671821"/><text x="92.1410%" y="239.50">alloc_pag..</text></g><g><title>__alloc_pages (1,671,821 samples, 7.00%)</title><rect x="91.8910%" y="213" width="7.0039%" height="15" fill="rgb(233,84,52)" fg:x="21934288" fg:w="1671821"/><text x="92.1410%" y="223.50">__alloc_p..</text></g><g><title>__memcg_kmem_charge_page (1,671,821 samples, 7.00%)</title><rect x="91.8910%" y="197" width="7.0039%" height="15" fill="rgb(207,117,47)" fg:x="21934288" fg:w="1671821"/><text x="92.1410%" y="207.50">__memcg_k..</text></g><g><title>memcg_account_kmem (1,671,821 samples, 7.00%)</title><rect x="91.8910%" y="181" width="7.0039%" height="15" fill="rgb(249,43,39)" fg:x="21934288" fg:w="1671821"/><text x="92.1410%" y="191.50">memcg_acc..</text></g><g><title>mod_memcg_state (1,671,821 samples, 7.00%)</title><rect x="91.8910%" y="165" width="7.0039%" height="15" fill="rgb(209,38,44)" fg:x="21934288" fg:w="1671821"/><text x="92.1410%" y="175.50">mod_memcg..</text></g><g><title>__mod_memcg_state (1,671,821 samples, 7.00%)</title><rect x="91.8910%" y="149" width="7.0039%" height="15" fill="rgb(236,212,23)" fg:x="21934288" fg:w="1671821"/><text x="92.1410%" y="159.50">__mod_mem..</text></g><g><title>debug_smp_processor_id (1,671,821 samples, 7.00%)</title><rect x="91.8910%" y="133" width="7.0039%" height="15" fill="rgb(242,79,21)" fg:x="21934288" fg:w="1671821"/><text x="92.1410%" y="143.50">debug_smp..</text></g><g><title>check_preemption_disabled (1,671,821 samples, 7.00%)</title><rect x="91.8910%" y="117" width="7.0039%" height="15" fill="rgb(211,96,35)" fg:x="21934288" fg:w="1671821"/><text x="92.1410%" y="127.50">check_pre..</text></g><g><title>all (23,869,899 samples, 100%)</title><rect x="0.0000%" y="517" width="100.0000%" height="15" fill="rgb(253,215,40)" fg:x="0" fg:w="23869899"/><text x="0.2500%" y="527.50"></text></g><g><title>perf-exec (263,790 samples, 1.11%)</title><rect x="98.8949%" y="501" width="1.1051%" height="15" fill="rgb(211,81,21)" fg:x="23606109" fg:w="263790"/><text x="99.1449%" y="511.50"></text></g><g><title>entry_SYSCALL_64_after_hwframe (263,790 samples, 1.11%)</title><rect x="98.8949%" y="485" width="1.1051%" height="15" fill="rgb(208,190,38)" fg:x="23606109" fg:w="263790"/><text x="99.1449%" y="495.50"></text></g><g><title>do_syscall_64 (263,790 samples, 1.11%)</title><rect x="98.8949%" y="469" width="1.1051%" height="15" fill="rgb(235,213,38)" fg:x="23606109" fg:w="263790"/><text x="99.1449%" y="479.50"></text></g><g><title>x64_sys_call (263,790 samples, 1.11%)</title><rect x="98.8949%" y="453" width="1.1051%" height="15" fill="rgb(237,122,38)" fg:x="23606109" fg:w="263790"/><text x="99.1449%" y="463.50"></text></g><g><title>__x64_sys_execve (263,790 samples, 1.11%)</title><rect x="98.8949%" y="437" width="1.1051%" height="15" fill="rgb(244,218,35)" fg:x="23606109" fg:w="263790"/><text x="99.1449%" y="447.50"></text></g><g><title>do_execveat_common.isra.0 (263,790 samples, 1.11%)</title><rect x="98.8949%" y="421" width="1.1051%" height="15" fill="rgb(240,68,47)" fg:x="23606109" fg:w="263790"/><text x="99.1449%" y="431.50"></text></g><g><title>bprm_execve (263,790 samples, 1.11%)</title><rect x="98.8949%" y="405" width="1.1051%" height="15" fill="rgb(210,16,53)" fg:x="23606109" fg:w="263790"/><text x="99.1449%" y="415.50"></text></g><g><title>load_elf_binary (263,790 samples, 1.11%)</title><rect x="98.8949%" y="389" width="1.1051%" height="15" fill="rgb(235,124,12)" fg:x="23606109" fg:w="263790"/><text x="99.1449%" y="399.50"></text></g><g><title>begin_new_exec (263,790 samples, 1.11%)</title><rect x="98.8949%" y="373" width="1.1051%" height="15" fill="rgb(224,169,11)" fg:x="23606109" fg:w="263790"/><text x="99.1449%" y="383.50"></text></g><g><title>perf_event_exec (263,790 samples, 1.11%)</title><rect x="98.8949%" y="357" width="1.1051%" height="15" fill="rgb(250,166,2)" fg:x="23606109" fg:w="263790"/><text x="99.1449%" y="367.50"></text></g><g><title>__x86_indirect_thunk_r13 (263,790 samples, 1.11%)</title><rect x="98.8949%" y="341" width="1.1051%" height="15" fill="rgb(242,216,29)" fg:x="23606109" fg:w="263790"/><text x="99.1449%" y="351.50"></text></g><g><title>asm_sysvec_irq_work (239,438 samples, 1.00%)</title><rect x="98.9969%" y="325" width="1.0031%" height="15" fill="rgb(230,116,27)" fg:x="23630461" fg:w="239438"/><text x="99.2469%" y="335.50"></text></g><g><title>sysvec_irq_work (239,438 samples, 1.00%)</title><rect x="98.9969%" y="309" width="1.0031%" height="15" fill="rgb(228,99,48)" fg:x="23630461" fg:w="239438"/><text x="99.2469%" y="319.50"></text></g><g><title>__sysvec_irq_work (239,438 samples, 1.00%)</title><rect x="98.9969%" y="293" width="1.0031%" height="15" fill="rgb(253,11,6)" fg:x="23630461" fg:w="239438"/><text x="99.2469%" y="303.50"></text></g><g><title>irq_work_run (239,438 samples, 1.00%)</title><rect x="98.9969%" y="277" width="1.0031%" height="15" fill="rgb(247,143,39)" fg:x="23630461" fg:w="239438"/><text x="99.2469%" y="287.50"></text></g><g><title>irq_work_run_list (239,438 samples, 1.00%)</title><rect x="98.9969%" y="261" width="1.0031%" height="15" fill="rgb(236,97,10)" fg:x="23630461" fg:w="239438"/><text x="99.2469%" y="271.50"></text></g><g><title>irq_work_single (239,438 samples, 1.00%)</title><rect x="98.9969%" y="245" width="1.0031%" height="15" fill="rgb(233,208,19)" fg:x="23630461" fg:w="239438"/><text x="99.2469%" y="255.50"></text></g><g><title>perf_pending_irq (239,438 samples, 1.00%)</title><rect x="98.9969%" y="229" width="1.0031%" height="15" fill="rgb(216,164,2)" fg:x="23630461" fg:w="239438"/><text x="99.2469%" y="239.50"></text></g><g><title>perf_event_wakeup (239,438 samples, 1.00%)</title><rect x="98.9969%" y="213" width="1.0031%" height="15" fill="rgb(220,129,5)" fg:x="23630461" fg:w="239438"/><text x="99.2469%" y="223.50"></text></g><g><title>__wake_up (239,438 samples, 1.00%)</title><rect x="98.9969%" y="197" width="1.0031%" height="15" fill="rgb(242,17,10)" fg:x="23630461" fg:w="239438"/><text x="99.2469%" y="207.50"></text></g><g><title>__wake_up_common_lock (239,438 samples, 1.00%)</title><rect x="98.9969%" y="181" width="1.0031%" height="15" fill="rgb(242,107,0)" fg:x="23630461" fg:w="239438"/><text x="99.2469%" y="191.50"></text></g><g><title>__wake_up_common (239,438 samples, 1.00%)</title><rect x="98.9969%" y="165" width="1.0031%" height="15" fill="rgb(251,28,31)" fg:x="23630461" fg:w="239438"/><text x="99.2469%" y="175.50"></text></g><g><title>pollwake (239,438 samples, 1.00%)</title><rect x="98.9969%" y="149" width="1.0031%" height="15" fill="rgb(233,223,10)" fg:x="23630461" fg:w="239438"/><text x="99.2469%" y="159.50"></text></g><g><title>default_wake_function (239,438 samples, 1.00%)</title><rect x="98.9969%" y="133" width="1.0031%" height="15" fill="rgb(215,21,27)" fg:x="23630461" fg:w="239438"/><text x="99.2469%" y="143.50"></text></g><g><title>try_to_wake_up (239,438 samples, 1.00%)</title><rect x="98.9969%" y="117" width="1.0031%" height="15" fill="rgb(232,23,21)" fg:x="23630461" fg:w="239438"/><text x="99.2469%" y="127.50"></text></g><g><title>ttwu_queue_wakelist (239,438 samples, 1.00%)</title><rect x="98.9969%" y="101" width="1.0031%" height="15" fill="rgb(244,5,23)" fg:x="23630461" fg:w="239438"/><text x="99.2469%" y="111.50"></text></g><g><title>__smp_call_single_queue (239,438 samples, 1.00%)</title><rect x="98.9969%" y="85" width="1.0031%" height="15" fill="rgb(226,81,46)" fg:x="23630461" fg:w="239438"/><text x="99.2469%" y="95.50"></text></g><g><title>native_send_call_func_single_ipi (239,438 samples, 1.00%)</title><rect x="98.9969%" y="69" width="1.0031%" height="15" fill="rgb(247,70,30)" fg:x="23630461" fg:w="239438"/><text x="99.2469%" y="79.50"></text></g><g><title>hv_send_ipi (239,438 samples, 1.00%)</title><rect x="98.9969%" y="53" width="1.0031%" height="15" fill="rgb(212,68,19)" fg:x="23630461" fg:w="239438"/><text x="99.2469%" y="63.50"></text></g><g><title>[[kernel.kallsyms]] (239,438 samples, 1.00%)</title><rect x="98.9969%" y="37" width="1.0031%" height="15" fill="rgb(240,187,13)" fg:x="23630461" fg:w="239438"/><text x="99.2469%" y="47.50"></text></g></svg></svg>