package main

import (
	"fmt"
	"os"
	"time"

	"nga-go/internal/config"
	"nga-go/internal/crawler"
	"nga-go/internal/database"
	"nga-go/internal/notifier"
	"nga-go/pkg/logger"

	"github.com/spf13/cobra"
)

var (
	// 全局标志
	configFile string
	verbose    bool

	// 根命令
	rootCmd = &cobra.Command{
		Use:   "nga-go",
		Short: "NGA 论坛数据爬虫 (Go 版本)",
		Long: `NGA 论坛数据爬虫的 Go 语言实现版本，支持：
- 主题数据抓取
- 用户回复监控
- Bark 推送通知
- 高性能并发处理`,
		PersistentPreRunE: initializeApp,
		PersistentPostRun: cleanupApp,
	}

	// 回复命令
	replyCmd = &cobra.Command{
		Use:   "reply",
		Short: "监控用户回复",
		Long:  "监控订阅用户的新回复，并发送 Bark 推送通知",
		RunE:  runReplyCommand,
	}

	// 主题命令
	subjectCmd = &cobra.Command{
		Use:   "subject",
		Short: "抓取主题数据",
		Long:  "抓取指定版块的主题列表数据",
		RunE:  runSubjectCommand,
	}

	// 帖子命令
	postCmd = &cobra.Command{
		Use:   "post",
		Short: "抓取帖子数据",
		Long:  "抓取指定主题的帖子内容",
		RunE:  runPostCommand,
	}
)

// 命令行参数
var (
	maxPages int
	tid      int
	fid      int = 706 // 默认版块ID
)

func init() {
	// 全局标志
	rootCmd.PersistentFlags().StringVar(&configFile, "config", "", "配置文件路径")
	rootCmd.PersistentFlags().BoolVarP(&verbose, "verbose", "v", false, "详细输出")

	// 主题命令参数
	subjectCmd.Flags().IntVar(&maxPages, "max-pages", 100, "最大抓取页数")
	subjectCmd.Flags().IntVar(&fid, "fid", 706, "版块ID")

	// 帖子命令参数
	postCmd.Flags().IntVarP(&tid, "tid", "t", 0, "主题ID")
	postCmd.MarkFlagRequired("tid")

	// 添加子命令
	rootCmd.AddCommand(replyCmd, subjectCmd, postCmd)
}

func main() {
	if err := rootCmd.Execute(); err != nil {
		fmt.Fprintf(os.Stderr, "错误: %v\n", err)
		os.Exit(1)
	}
}

// initializeApp 初始化应用程序
func initializeApp(cmd *cobra.Command, args []string) error {
	// 初始化配置
	if err := config.Init(); err != nil {
		return fmt.Errorf("初始化配置失败: %w", err)
	}

	// 如果启用了 verbose 模式，设置日志级别为 debug
	if verbose {
		config.AppConfig.Log.Level = "debug"
	}

	// 初始化日志系统
	if err := logger.Init(); err != nil {
		return fmt.Errorf("初始化日志系统失败: %w", err)
	}

	// 初始化数据库
	if err := database.Init(); err != nil {
		return fmt.Errorf("初始化数据库失败: %w", err)
	}

	logger.Info("应用程序初始化完成",
		"version", "2.0.0",
		"command", cmd.Name())

	return nil
}

// cleanupApp 清理应用程序资源
func cleanupApp(cmd *cobra.Command, args []string) {
	if err := database.Close(); err != nil {
		logger.Error("关闭数据库连接失败", "error", err)
	}
	logger.Info("应用程序退出")
}

// runReplyCommand 运行回复监控命令
func runReplyCommand(cmd *cobra.Command, args []string) error {
	startTime := time.Now()

	// 创建 Bark 通知器
	bark := notifier.NewBarkNotifier(&config.AppConfig.Bark)

	// 发送启动通知
	if bark.IsEnabled() {
		if err := bark.SendStartNotification("回复监控"); err != nil {
			logger.Warn("发送启动通知失败", "error", err)
		}
	}

	// 创建爬虫实例
	ngaCrawler := crawler.NewNGACrawler(bark)

	// 处理回复数据
	if err := ngaCrawler.ProcessReplies(); err != nil {
		logger.Error("处理回复数据失败", "error", err)

		// 发送错误通知
		if bark.IsEnabled() {
			bark.SendErrorNotification(fmt.Sprintf("回复监控失败: %v", err))
		}

		return err
	}

	duration := time.Since(startTime)
	logger.Info("回复监控完成", "duration", duration)

	// 发送完成通知
	if bark.IsEnabled() {
		if err := bark.SendCompleteNotification("回复监控", duration); err != nil {
			logger.Warn("发送完成通知失败", "error", err)
		}
	}

	return nil
}

// runSubjectCommand 运行主题抓取命令
func runSubjectCommand(cmd *cobra.Command, args []string) error {
	startTime := time.Now()

	// 创建 Bark 通知器
	bark := notifier.NewBarkNotifier(&config.AppConfig.Bark)

	// 发送启动通知
	if bark.IsEnabled() {
		if err := bark.SendStartNotification(fmt.Sprintf("主题抓取 (版块:%d)", fid)); err != nil {
			logger.Warn("发送启动通知失败", "error", err)
		}
	}

	// 创建爬虫实例
	ngaCrawler := crawler.NewNGACrawler(bark)

	// 处理主题数据
	if err := ngaCrawler.ProcessSubjects(maxPages); err != nil {
		logger.Error("处理主题数据失败", "error", err)

		// 发送错误通知
		if bark.IsEnabled() {
			bark.SendErrorNotification(fmt.Sprintf("主题抓取失败: %v", err))
		}

		return err
	}

	duration := time.Since(startTime)
	logger.Info("主题抓取完成",
		"max_pages", maxPages,
		"fid", fid,
		"duration", duration)

	// 发送完成通知
	if bark.IsEnabled() {
		if err := bark.SendCompleteNotification(
			fmt.Sprintf("主题抓取 (版块:%d)", fid),
			duration); err != nil {
			logger.Warn("发送完成通知失败", "error", err)
		}
	}

	return nil
}

// runPostCommand 运行帖子抓取命令
func runPostCommand(cmd *cobra.Command, args []string) error {
	startTime := time.Now()

	// 创建 Bark 通知器
	bark := notifier.NewBarkNotifier(&config.AppConfig.Bark)

	// 发送启动通知
	if bark.IsEnabled() {
		if err := bark.SendStartNotification(fmt.Sprintf("帖子抓取 (主题:%d)", tid)); err != nil {
			logger.Warn("发送启动通知失败", "error", err)
		}
	}

	// TODO: 实现帖子抓取逻辑
	logger.Info("帖子抓取功能开发中", "tid", tid)

	duration := time.Since(startTime)
	logger.Info("帖子抓取完成",
		"tid", tid,
		"duration", duration)

	// 发送完成通知
	if bark.IsEnabled() {
		if err := bark.SendCompleteNotification(
			fmt.Sprintf("帖子抓取 (主题:%d)", tid),
			duration); err != nil {
			logger.Warn("发送完成通知失败", "error", err)
		}
	}

	return nil
}
