use macros::watch_time;
use serde::{Deserialize, Serialize};
use std::hash::Hash;

#[test]
pub fn a() {
    let encode = jsonwebtoken::encode(
        &jsonwebtoken::Header::default(),
        &Claims {
            sub: "1234567890".to_string(),
            name: "<PERSON>".to_string(),
            admin: true,
            exp: 10000000000,
        },
        &jsonwebtoken::EncodingKey::from_secret("secret".as_ref()),
    ).unwrap();
    let _ = watch_time!(
        "hash", bcrypt::hash("sdfasdfds", 13)
    );
}

#[derive(Serialize, Deserialize)]
struct Claims {
    sub: String,
    name: String,
    admin: bool,
    exp: usize,
}