[package]
name = "app"
version = "0.1.0"
edition = "2021"

[dependencies]
sysinfo = {version = "*"}
tokio = {version = "*", features = ["full"]}
mycore= { path = "../mycore" }
log = "0.4.27"
jsonwebtoken = {version = "9.3.1"}
serde = { version = "1.0.219", features = ["derive"] }
bcrypt = {version = "0.17.0", features = ["default"]}
macros= { path = "../macros" }
duckdb = { version = "1.3.0", features = ["bundled"] }