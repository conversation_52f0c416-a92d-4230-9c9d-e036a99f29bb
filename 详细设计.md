
你现在是一个资深票务架构设计师,你现在需要详细设计其中的一个模块《查票模块》,
该模块负责通过输入的核销码以及核销码类型来初次筛选当前检票景点可以使用的条码以及其他核销业务的明细，并支持按指定规则进行排序。
注：初筛指的是不考虑任何核验规则的情况下，只根据检票景点判断当前核销码是否可在当前点位使用。
他也支持多种方式查票,比如条码,身份证,ic卡,人脸等,你里面的票也来自各个系统,比如自身票务系统,以及各种外部系统,比如文旅卡, 最后需要进行各种规则排序输出. 推荐使用es实现数据存储,加快数据查询，还有利用dubbo以及消息中间件录入其他票的维度信息, 目前还有一种场景， 对于一些第三方票， 只提供检票接口， 需要返回票 并 在检票流程后再来检，对外最好是提供http/dubbo接口 供外部调用， 需要加入其他的思考 比如加入场景id， 区分具体各种场景门票，比如湖泊，山岳等


我优化后的结构化Prompt如下，结合了系统设计要点和提示词工程最佳实践（参考[<sup>1</sup>](https://github.com/lijigang/prompts)和[<sup>2</sup>](https://github.com/langgptai/LangGPT/blob/main/Docs/HowToWritestructuredPrompts.md)）：

**角色**：资深票务系统架构师
**任务**：设计高并发查票模块，需满足：
```
1. 核心功能
   - 多方式查询：支持条码/身份证/IC卡/人脸等（需设计统一查询适配层）
   - 初筛机制：仅根据检票点ID过滤可用票，不触发核验规则
   - 多源票务：整合自有系统+文旅卡等外部系统（需设计数据同步方案）
   - 动态排序：支持按票价/有效期/票源优先级等规则排序

2. 技术实现
   ｜━━ ES存储设计 ｜
   • 索引结构：票ID、场景ID、核销码哈希、票源系统、有效期范围、使用状态等
   • 查询优化：为核销码类型+场景ID建立组合索引，使用filter context加速初筛
 
   ｜━━ 服务架构 ｜
   • 使用Dubbo构建微服务，通过SPI机制扩展第三方票源适配器
   • 消息中间件（Kafka）同步外部票务数据变更
   • 第三方检票接口封装为Fallback模式：先返回本地票状态，异步触发远程核验

3. 扩展设计
   - 场景ID体系：定义山岳(1001)/湖泊(1002)/剧院(1003)等场景类型
   - 熔断机制：第三方接口超时自动降级为本地验证
```

**输出要求**（参考[<sup>3</sup>](https://ai.ls/zh-cn/chatgpt-prompts/xiaohongshu-wenan-prompt/)）：
1. 先输出架构图（mermaid语法）
2. 分模块说明核心交互流程
3. 给出ES mapping示例和Dubbo接口定义
4. 针对高并发场景提出3个优化方案

**示例思考**（类似[<sup>4</sup>](https://codemia.io/system-design/design-ticketmaster/solutions/s9xmtk/Design-Ticketmaster-with-Score-810)的影院设计）：
当用户在山岳景区（场景ID=1001）刷身份证时，系统应：
1. 通过ES快速过滤该场景有效票
2. 优先展示当日有效的年卡票
3. 异步调用文旅局接口验证区域通行权限
4. 返回带加密水印的电子票二维码