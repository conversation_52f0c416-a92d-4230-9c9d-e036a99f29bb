package notifier

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"nga-go/internal/config"
	"nga-go/pkg/logger"

	"github.com/go-resty/resty/v2"
)

// BarkNotifier Bark 推送通知器
type BarkNotifier struct {
	config *config.BarkConfig
	client *resty.Client
}

// BarkRequest Bark 推送请求结构
type BarkRequest struct {
	Title    string `json:"title"`
	Body     string `json:"body"`
	Priority int    `json:"priority,omitempty"`
	URL      string `json:"url,omitempty"`
	Group    string `json:"group,omitempty"`
}

// BarkResponse Bark 推送响应结构
type BarkResponse struct {
	Code      int    `json:"code"`
	Message   string `json:"message"`
	Timestamp int64  `json:"timestamp"`
}

// NewBarkNotifier 创建新的 Bark 通知器
func NewBarkNotifier(cfg *config.BarkConfig) *BarkNotifier {
	if !cfg.Enabled {
		return &BarkNotifier{config: cfg}
	}

	client := resty.New()
	client.SetTimeout(10 * time.Second)
	client.SetRetryCount(2)
	client.SetRetryWaitTime(1 * time.Second)

	return &BarkNotifier{
		config: cfg,
		client: client,
	}
}

// SendNotification 发送 Bark 推送通知
func (b *BarkNotifier) SendNotification(title, body string, priority ...int) error {
	if !b.config.Enabled || b.client == nil {
		logger.Debug("Bark 推送未启用或客户端未初始化")
		return nil
	}

	if b.config.ServerURL == "" || b.config.DeviceKey == "" {
		return fmt.Errorf("Bark 配置不完整: server_url=%s, device_key=%s",
			b.config.ServerURL, maskDeviceKey(b.config.DeviceKey))
	}

	// 构建请求
	req := &BarkRequest{
		Title: truncateString(title, 100),
		Body:  truncateString(body, 1000),
		Group: "NGA爬虫",
	}

	// 设置优先级
	if len(priority) > 0 {
		req.Priority = priority[0]
	} else {
		req.Priority = b.config.DefaultPriority
	}

	// 发送请求
	url := fmt.Sprintf("%s/%s",
		strings.TrimRight(b.config.ServerURL, "/"),
		b.config.DeviceKey)

	resp, err := b.client.R().
		SetHeader("Content-Type", "application/json").
		SetBody(req).
		Post(url)

	if err != nil {
		return fmt.Errorf("Bark 推送请求失败: %w", err)
	}

	// 检查响应
	if resp.StatusCode() != 200 {
		return fmt.Errorf("Bark 推送失败, 状态码: %d, 响应: %s",
			resp.StatusCode(), resp.String())
	}

	// 解析响应
	var barkResp BarkResponse
	if err := json.Unmarshal(resp.Body(), &barkResp); err != nil {
		logger.Warn("解析 Bark 响应失败", "error", err, "response", resp.String())
		// 即使解析失败，只要状态码是 200 就认为成功
	}

	logger.Debug("Bark 推送成功", "title", title)
	return nil
}

// SendNGAReplyNotification 发送 NGA 回复通知
func (b *BarkNotifier) SendNGAReplyNotification(author, content, postdate string, tid int) error {
	title := fmt.Sprintf("NGA新回复 - %s", author)

	bodyParts := []string{
		fmt.Sprintf("时间: %s", postdate),
		fmt.Sprintf("内容: %s", truncateString(content, 200)),
	}

	if tid > 0 {
		bodyParts = append(bodyParts, fmt.Sprintf("主题ID: %d", tid))
	}

	body := strings.Join(bodyParts, "\n")

	return b.SendNotification(title, body, 1) // 新回复使用中等优先级
}

// SendErrorNotification 发送错误通知
func (b *BarkNotifier) SendErrorNotification(errorMsg string) error {
	title := "NGA爬虫错误"
	return b.SendNotification(title, errorMsg, 2) // 错误使用高优先级
}

// SendStartNotification 发送启动通知
func (b *BarkNotifier) SendStartNotification(mode string) error {
	title := "NGA爬虫启动"
	body := fmt.Sprintf("爬虫已启动，运行模式: %s\n时间: %s",
		mode, time.Now().Format("2006-01-02 15:04:05"))
	return b.SendNotification(title, body, 0)
}

// SendCompleteNotification 发送完成通知
func (b *BarkNotifier) SendCompleteNotification(mode string, duration time.Duration) error {
	title := "NGA爬虫完成"
	body := fmt.Sprintf("爬虫任务完成\n模式: %s\n耗时: %s\n时间: %s",
		mode, duration.String(), time.Now().Format("2006-01-02 15:04:05"))
	return b.SendNotification(title, body, 0)
}

// IsEnabled 检查 Bark 推送是否启用
func (b *BarkNotifier) IsEnabled() bool {
	return b.config.Enabled && b.client != nil
}

// truncateString 截断字符串到指定长度
func truncateString(s string, maxLen int) string {
	if len(s) <= maxLen {
		return s
	}
	return s[:maxLen] + "..."
}

// maskDeviceKey 对设备密钥进行脱敏处理
func maskDeviceKey(key string) string {
	if len(key) <= 4 {
		return "****"
	}
	return key[:2] + "****" + key[len(key)-2:]
}
