---
description: 
globs: 
alwaysApply: false
---
# 开发指南

## 环境要求

- Rust 2021 版本
- Docker（用于容器化部署）
- Git

## 开发流程

1. 克隆项目后，首先运行 `cargo build` 确保所有依赖正确安装
2. 使用 `cargo test` 运行测试
3. 开发新功能时，请在相应的模块目录下进行开发
4. 提交代码前运行 `cargo fmt` 和 `cargo clippy` 确保代码质量

## 项目依赖

主要依赖包括：
- serde_json - JSON 序列化/反序列化
- tokio - 异步运行时
- tracing - 日志追踪
- 自定义模块：
  - mycore
  - web1
  - web2
  - app

## 构建和运行

### 开发环境
```bash
cargo run
```

### 生产环境
```bash
cargo build --release
```

### Docker 部署
```bash
docker-compose up -d
```

