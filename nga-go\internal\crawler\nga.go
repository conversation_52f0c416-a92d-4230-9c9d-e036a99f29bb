package crawler

import (
	"encoding/json"
	"fmt"
	"math/rand"
	"regexp"
	"strconv"
	"strings"
	"time"

	"nga-go/internal/config"
	"nga-go/internal/database"
	"nga-go/internal/models"
	"nga-go/internal/notifier"
	"nga-go/pkg/logger"

	"github.com/go-resty/resty/v2"
	"gorm.io/gorm"
)

// NGACrawler NGA 爬虫结构
type NGACrawler struct {
	client   *resty.Client
	notifier *notifier.BarkNotifier
	db       *gorm.DB

	// 预编译的正则表达式
	trimQuote  *regexp.Regexp
	trimB      *regexp.Regexp
	trimBR     *regexp.Regexp
	replaceURL *regexp.Regexp
}

// NGAResponse NGA API 响应结构
type NGAResponse struct {
	Code   int                    `json:"code"`
	Result map[string]interface{} `json:"result"`
}

// NewNGACrawler 创建新的 NGA 爬虫实例
func NewNGACrawler(bark *notifier.BarkNotifier) *NGACrawler {
	client := resty.New()
	client.SetTimeout(30 * time.Second)
	client.SetRetryCount(3)
	client.SetRetryWaitTime(2 * time.Second)

	// 设置请求头
	client.SetHeaders(config.AppConfig.NGA.Headers)
	if config.AppConfig.NGA.Cookie != "" {
		client.SetHeader("Cookie", config.AppConfig.NGA.Cookie)
	}

	crawler := &NGACrawler{
		client:   client,
		notifier: bark,
		db:       database.GetDB(),
	}

	// 预编译正则表达式
	crawler.compileRegexps()

	return crawler
}

// compileRegexps 预编译正则表达式
func (c *NGACrawler) compileRegexps() {
	c.trimQuote = regexp.MustCompile(`^\[quote\].*?\[uid.*?\](.*)\[\/uid\].*?<br\/>(.*?)\[\/quote\]`)
	c.trimB = regexp.MustCompile(`^\[b\].*?\[uid.*?\](.*)\[\/uid\].*?\[\/b\](.*?)`)
	c.trimBR = regexp.MustCompile(`<br\/>|\n`)
	c.replaceURL = regexp.MustCompile(`\[img\]\.(.*)\.jpg\[\/img\]`)
}

// makeRequest 发送 HTTP 请求的通用方法
func (c *NGACrawler) makeRequest(endpoint string, data map[string]interface{}) (*NGAResponse, error) {
	url := config.AppConfig.NGA.PrefixURL + endpoint

	resp, err := c.client.R().
		SetFormData(convertToStringMap(data)).
		Post(url)

	if err != nil {
		return nil, fmt.Errorf("请求失败: %w", err)
	}

	var ngaResp NGAResponse
	if err := json.Unmarshal(resp.Body(), &ngaResp); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	if ngaResp.Code != 0 {
		return nil, fmt.Errorf("API 返回错误: code=%d", ngaResp.Code)
	}

	return &ngaResp, nil
}

// GetSubjectList 获取主题列表
func (c *NGACrawler) GetSubjectList(fid, page int) (*NGAResponse, error) {
	return c.makeRequest("__lib=subject&__act=list", map[string]interface{}{
		"fid":  fid,
		"page": page,
	})
}

// GetPostList 获取帖子列表
func (c *NGACrawler) GetPostList(tid, page int) (*NGAResponse, error) {
	return c.makeRequest("__lib=post&__act=list", map[string]interface{}{
		"tid":  tid,
		"page": page,
	})
}

// GetReplyList 获取用户回复列表
func (c *NGACrawler) GetReplyList(uid, page int) (*NGAResponse, error) {
	return c.makeRequest("__lib=user&__act=replys", map[string]interface{}{
		"uid":  uid,
		"page": page,
	})
}

// ProcessSubjects 处理主题数据
func (c *NGACrawler) ProcessSubjects(maxPages int) error {
	logger.Info("开始处理主题数据", "max_pages", maxPages)

	for page := 1; page <= maxPages; page++ {
		resp, err := c.GetSubjectList(706, page) // 默认版块 ID
		if err != nil {
			logger.Error("获取主题列表失败", "page", page, "error", err)
			continue
		}

		data, ok := resp.Result["data"].([]interface{})
		if !ok {
			logger.Warn("主题数据格式错误", "page", page)
			continue
		}

		for _, item := range data {
			if err := c.processSubjectItem(item); err != nil {
				logger.Error("处理主题项失败", "error", err)
				continue
			}
		}

		// 随机延时
		time.Sleep(time.Duration(rand.Intn(4)+1) * time.Second)

		if page%100 == 0 {
			logger.Info("已处理主题页数", "page", page)
		}
	}

	return nil
}

// processSubjectItem 处理单个主题项
func (c *NGACrawler) processSubjectItem(item interface{}) error {
	itemMap, ok := item.(map[string]interface{})
	if !ok {
		return fmt.Errorf("主题项格式错误")
	}

	subject := &models.NGASubject{}

	// 转换字段
	if tid, ok := getIntValue(itemMap, "tid"); ok {
		subject.TID = tid
	}
	if fid, ok := getIntValue(itemMap, "fid"); ok {
		subject.FID = fid
	}
	if author, ok := getStringValue(itemMap, "author"); ok {
		subject.Author = author
	}
	if authorid, ok := getIntValue(itemMap, "authorid"); ok {
		subject.AuthorID = authorid
	}
	if subjectTitle, ok := getStringValue(itemMap, "subject"); ok {
		subject.Subject = subjectTitle
	}
	if postdate, ok := getIntValue(itemMap, "postdate"); ok {
		subject.PostDate = time.Unix(int64(postdate), 0)
	}
	if lastpost, ok := getIntValue(itemMap, "lastpost"); ok {
		subject.LastPost = time.Unix(int64(lastpost), 0)
	}
	if lastposter, ok := getStringValue(itemMap, "lastposter"); ok {
		subject.LastPoster = lastposter
	}
	if replies, ok := getIntValue(itemMap, "replies"); ok {
		subject.Replies = replies
	}

	// 使用 GORM 的 upsert 功能
	return c.db.Save(subject).Error
}

// ProcessReplies 处理用户回复数据
func (c *NGACrawler) ProcessReplies() error {
	logger.Info("开始处理回复数据")

	// 获取订阅的作者
	var authors []models.NGAAuthor
	if err := c.db.Where("sub = ?", 1).Find(&authors).Error; err != nil {
		return fmt.Errorf("获取订阅作者失败: %w", err)
	}

	for _, author := range authors {
		if err := c.processAuthorReplies(&author); err != nil {
			logger.Error("处理作者回复失败", "username", author.Username, "error", err)
			continue
		}

		// 请求间隔
		time.Sleep(2 * time.Second)
	}

	return nil
}

// processAuthorReplies 处理单个作者的回复
func (c *NGACrawler) processAuthorReplies(author *models.NGAAuthor) error {
	resp, err := c.GetReplyList(author.UID, 1)
	if err != nil {
		return fmt.Errorf("获取作者回复失败: %w", err)
	}

	data, ok := resp.Result["data"].([]interface{})
	if !ok {
		return fmt.Errorf("回复数据格式错误")
	}

	newRepliesCount := 0
	var lastReplyTime *time.Time

	for _, item := range data {
		itemMap, ok := item.(map[string]interface{})
		if !ok {
			continue
		}

		postMap, ok := itemMap["post"].(map[string]interface{})
		if !ok {
			continue
		}

		postdateValue, exists := postMap["postdate"]
		if !exists {
			continue
		}

		// 处理时间戳（可能是字符串或数字）
		var postdate time.Time
		switch v := postdateValue.(type) {
		case float64:
			postdate = time.Unix(int64(v), 0)
		case string:
			if v == "" {
				continue
			}
			if timestamp, err := strconv.ParseInt(v, 10, 64); err == nil {
				postdate = time.Unix(timestamp, 0)
			} else {
				continue
			}
		default:
			continue
		}

		// 检查是否为新回复
		if author.LastReplyTime == nil || postdate.After(*author.LastReplyTime) {
			content, _ := getStringValue(postMap, "content")
			formattedContent := c.formatContent(content)

			logger.Info("发现新回复",
				"username", author.Username,
				"postdate", postdate.Format("2006-01-02 15:04:05"),
				"content", truncateString(formattedContent, 100))

			// 发送 Bark 推送
			if c.notifier != nil && c.notifier.IsEnabled() {
				tid, _ := getIntValue(itemMap, "tid")
				if err := c.notifier.SendNGAReplyNotification(
					author.Username,
					formattedContent,
					postdate.Format("2006-01-02 15:04:05"),
					tid,
				); err != nil {
					logger.Error("Bark 推送失败", "error", err)
				}
			}

			newRepliesCount++
		}

		// 更新最新回复时间
		if lastReplyTime == nil || postdate.After(*lastReplyTime) {
			lastReplyTime = &postdate
		}
	}

	// 更新作者的最后回复时间
	if lastReplyTime != nil && (author.LastReplyTime == nil || lastReplyTime.After(*author.LastReplyTime)) {
		author.LastReplyTime = lastReplyTime
		if err := c.db.Save(author).Error; err != nil {
			return fmt.Errorf("更新作者回复时间失败: %w", err)
		}

		logger.Info("作者回复处理完成",
			"username", author.Username,
			"new_replies", newRepliesCount)
	}

	return nil
}

// formatContent 格式化回复内容
func (c *NGACrawler) formatContent(content string) string {
	// 处理引用
	content = c.trimQuote.ReplaceAllString(content, "($1)$2||")
	// 处理粗体
	content = c.trimB.ReplaceAllString(content, "($1)$2||")
	// 处理换行
	content = c.trimBR.ReplaceAllString(content, " ")
	// 处理图片链接
	content = c.replaceURL.ReplaceAllString(content, "https://img.nga.178.com/attachments$1.jpg")

	return strings.TrimSpace(content)
}

// 工具函数
func convertToStringMap(data map[string]interface{}) map[string]string {
	result := make(map[string]string)
	for k, v := range data {
		result[k] = fmt.Sprintf("%v", v)
	}
	return result
}

func getIntValue(m map[string]interface{}, key string) (int, bool) {
	value, exists := m[key]
	if !exists {
		return 0, false
	}

	switch v := value.(type) {
	case int:
		return v, true
	case float64:
		return int(v), true
	case string:
		if i, err := strconv.Atoi(v); err == nil {
			return i, true
		}
	}
	return 0, false
}

func getStringValue(m map[string]interface{}, key string) (string, bool) {
	value, exists := m[key]
	if !exists {
		return "", false
	}

	if s, ok := value.(string); ok {
		return s, true
	}
	return fmt.Sprintf("%v", value), true
}

func truncateString(s string, maxLen int) string {
	if len(s) <= maxLen {
		return s
	}
	return s[:maxLen] + "..."
}
