"""
NGA 论坛数据爬虫主模块
优化版本，支持 Bark 推送通知
"""
import argparse
import datetime
import logging
import random
import re
import time
from contextlib import contextmanager
from typing import Dict, Optional, Any, Generator

import httpx
import sqlalchemy
from sqlalchemy import Column, Integer, String, TIMESTAMP, UniqueConstraint, update
from sqlalchemy.dialects.postgresql import insert
from sqlalchemy.orm import declarative_base, sessionmaker, Session

from config import config
from notifier import BarkNotifier

# 配置日志
logging.basicConfig(
    format=config.log.format,
    datefmt=config.log.date_format,
    level=getattr(logging, config.log.level.upper()),
    filename=config.log.file_path
)

# 减少 httpx 日志噪音
logging.getLogger('httpx').setLevel(logging.WARNING)

Base = declarative_base()


class NGAAuthor(Base):
    """NGA 作者信息表"""
    __tablename__ = 'nga_author'

    id = Column(Integer, primary_key=True, comment='主键ID')
    username = Column(String(200), comment='用户名')
    uid = Column(Integer, comment='用户ID')
    timeDelaySec = Column(Integer, comment='用户发帖延时')
    lastReplyTime = Column(TIMESTAMP, comment='上次回复时间')
    sub = Column(Integer, comment='是否订阅')


class NGASubject(Base):
    """NGA 主题信息表"""
    __tablename__ = 'nga_subjects'

    id = Column(Integer, primary_key=True, comment='主键ID')
    tid = Column(Integer, comment='主题ID', unique=True)
    fid = Column(Integer, comment='版块ID')
    author = Column(String(200), comment='作者')
    authorid = Column(Integer, comment='作者ID')
    subject = Column(String(200), comment='主题')
    postdate = Column(TIMESTAMP, comment='发表时间')
    lastpost = Column(TIMESTAMP, comment='最后发表时间')
    lastposter = Column(String(200), comment='最后发表作者')
    replies = Column(Integer, comment='回复数')

    UniqueConstraint('tid', 'fid', name='uq_tid_fid')


class NGACrawler:
    """NGA 爬虫主类"""
    
    # 预编译正则表达式，提高性能
    TRIM_QUOTE = re.compile(r"^\[quote].*?\[uid.*?](.*)\[\/uid].*?<br\/>(.*?)\[\/quote]")
    TRIM_B = re.compile(r"^\[b].*?\[uid.*?](.*)\[\/uid].*?\[\/b](.*?)")
    TRIM_BR = re.compile(r"<br\/>|\n")
    REPLACE_URL = re.compile(r"\[img]\.(.*)\.jpg\[\/img]")

    def __init__(self, notifier: Optional[BarkNotifier] = None):
        """
        初始化 NGA 爬虫
        
        Args:
            notifier: Bark 推送通知器，可选
        """
        self.headers = config.nga.headers.copy()
        if config.nga.cookie:
            self.headers['cookie'] = config.nga.cookie
            
        self.client = httpx.Client(headers=self.headers, timeout=30.0)
        self.notifier = notifier
        
        # 初始化数据库
        self.engine = sqlalchemy.create_engine(
            config.database.url, 
            echo=config.database.echo
        )
        self.SessionLocal = sessionmaker(bind=self.engine, autoflush=True)
        Base.metadata.create_all(self.engine)

    def __del__(self):
        """清理资源"""
        if hasattr(self, 'client'):
            self.client.close()

    @contextmanager
    def get_db_session(self) -> Generator[Session, None, None]:
        """
        数据库会话上下文管理器
        
        Yields:
            Session: 数据库会话对象
        """
        session = self.SessionLocal()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            logging.error(f"数据库操作错误: {e}")
            if self.notifier:
                self.notifier.send_error_notification(f"数据库错误: {str(e)}")
            raise
        finally:
            session.close()

    def _make_request(self, endpoint: str, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        发送 HTTP 请求的通用方法
        
        Args:
            endpoint: API 端点
            data: 请求数据
            
        Returns:
            Optional[Dict]: 响应 JSON 数据，失败时返回 None
        """
        try:
            url = config.nga.prefix_url + endpoint
            response = self.client.post(url, data=data)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logging.error(f"请求失败 {endpoint}: {e}")
            if self.notifier:
                self.notifier.send_error_notification(f"API请求失败: {str(e)}")
            return None

    def get_subject_list(self, fid: int = 706, page: int = 1) -> Optional[Dict[str, Any]]:
        """
        获取主题列表
        
        Args:
            fid: 版块ID，默认706
            page: 页码，默认1
            
        Returns:
            Optional[Dict]: 主题列表数据
        """
        return self._make_request("__lib=subject&__act=list", {"fid": fid, "page": page})

    def get_post_list(self, tid: int, page: int = 1) -> Optional[Dict[str, Any]]:
        """
        获取帖子列表
        
        Args:
            tid: 主题ID
            page: 页码，默认1
            
        Returns:
            Optional[Dict]: 帖子列表数据
        """
        return self._make_request("__lib=post&__act=list", {"tid": tid, "page": page})

    def get_reply_list(self, uid: int, page: int = 1) -> Optional[Dict[str, Any]]:
        """
        获取用户回复列表
        
        Args:
            uid: 用户ID
            page: 页码，默认1
            
        Returns:
            Optional[Dict]: 回复列表数据
        """
        return self._make_request("__lib=user&__act=replys", {"uid": uid, "page": page})

    def _format_content(self, content: str) -> str:
        """
        格式化回复内容
        
        Args:
            content: 原始内容
            
        Returns:
            str: 格式化后的内容
        """
        # 处理引用
        content = self.TRIM_QUOTE.sub(r"(\1)\2||", content)
        # 处理粗体
        content = self.TRIM_B.sub(r"(\1)\2||", content)
        # 处理换行
        content = self.TRIM_BR.sub(" ", content)
        # 处理图片链接
        content = self.REPLACE_URL.sub(r"https://img.nga.178.com/attachments\1.jpg", content)
        
        return content.strip()

    def process_subjects(self, max_pages: int = 5000) -> None:
        """
        处理主题数据
        
        Args:
            max_pages: 最大页数
        """
        logging.info("开始处理主题数据")
        
        with self.get_db_session() as session:
            for page in range(1, max_pages + 1):
                resp = self.get_subject_list(page=page)
                if not resp or resp.get('code') != 0:
                    logging.warning(f"获取主题列表失败，页码: {page}")
                    continue
                
                for data in resp.get('result', {}).get('data', []):
                    try:
                        # 提取数据库字段
                        nga_subject = {k: v for k, v in data.items() if hasattr(NGASubject, k)}
                        
                        # 转换时间戳
                        if 'postdate' in nga_subject:
                            nga_subject['postdate'] = datetime.datetime.fromtimestamp(
                                nga_subject['postdate'], 
                                datetime.timezone(offset=datetime.timedelta(hours=8))
                            )
                        if 'lastpost' in nga_subject:
                            nga_subject['lastpost'] = datetime.datetime.fromtimestamp(
                                nga_subject['lastpost'], 
                                datetime.timezone(offset=datetime.timedelta(hours=8))
                            )
                        
                        # 使用 upsert 操作
                        stmt = insert(NGASubject).values(nga_subject)
                        stmt = stmt.on_duplicate_key_update(**nga_subject)
                        session.execute(stmt)
                        
                    except Exception as e:
                        logging.error(f"处理主题数据失败: {e}")
                        continue
                
                # 随机延时，避免频繁请求
                time.sleep(random.randint(1, 5))
                
                if page % 100 == 0:
                    logging.info(f"已处理 {page} 页主题数据")

    def process_replies(self) -> None:
        """处理用户回复数据"""
        logging.info("开始处理回复数据")
        
        with self.get_db_session() as session:
            # 获取订阅的作者
            authors = session.query(NGAAuthor).filter(NGAAuthor.sub == 1).all()
            
            for author in authors:
                try:
                    self._process_author_replies(session, author)
                    time.sleep(2)  # 请求间隔
                except Exception as e:
                    logging.error(f"处理作者 {author.username} 回复失败: {e}")
                    continue

    def _process_author_replies(self, session: Session, author: NGAAuthor) -> None:
        """
        处理单个作者的回复
        
        Args:
            session: 数据库会话
            author: 作者对象
        """
        resp = self.get_reply_list(uid=author.uid)
        if not resp or resp.get('code') != 0:
            logging.warning(f"获取作者 {author.username} 回复失败")
            return
        
        last_reply_time = author.lastReplyTime
        new_replies_count = 0
        
        for data in resp.get('result', {}).get('data', []):
            post_data = data.get('post', {})
            if not post_data.get('postdate'):
                continue
                
            try:
                postdate = datetime.datetime.fromtimestamp(post_data['postdate'])
                
                # 检查是否为新回复
                if author.lastReplyTime is None or postdate > author.lastReplyTime:
                    content = self._format_content(post_data.get('content', ''))
                    formatted_content = f"{author.username}, {postdate}, {content}"
                    
                    logging.info(f"新回复: {formatted_content}")
                    
                    # 发送 Bark 推送
                    if self.notifier:
                        self.notifier.send_nga_reply_notification(
                            author=author.username,
                            content=content,
                            postdate=postdate.strftime('%Y-%m-%d %H:%M:%S'),
                            tid=data.get('tid')
                        )
                    
                    new_replies_count += 1
                
                # 更新最新回复时间
                if last_reply_time is None or postdate > last_reply_time:
                    last_reply_time = postdate
                    
            except Exception as e:
                logging.error(f"处理回复数据失败: {e}")
                continue
        
        # 更新作者的最后回复时间
        if last_reply_time != author.lastReplyTime:
            stmt = update(NGAAuthor).values(lastReplyTime=last_reply_time).where(
                NGAAuthor.uid == author.uid
            )
            session.execute(stmt)
            logging.info(f"作者 {author.username} 发现 {new_replies_count} 条新回复")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='NGA 论坛数据爬虫')
    parser.add_argument("-m", "--method", default='reply', choices=['subject', 'post', 'reply'],
                       help='运行模式: subject(主题), post(帖子), reply(回复)')
    parser.add_argument("-t", "--tid", type=int, help='主题ID (仅用于post模式)')
    
    args = parser.parse_args()
    
    # 初始化 Bark 推送器
    notifier = BarkNotifier(config.bark) if config.bark.enabled else None
    
    # 初始化爬虫
    crawler = NGACrawler(notifier=notifier)
    
    try:
        if args.method == 'subject':
            crawler.process_subjects()
        elif args.method == 'post':
            if args.tid is None:
                logging.error("post 模式需要指定 --tid 参数")
                return
            # TODO: 实现帖子处理逻辑
            logging.info(f"处理主题 {args.tid} 的帖子数据")
        elif args.method == 'reply':
            crawler.process_replies()
            
        logging.info("任务完成")
        
    except KeyboardInterrupt:
        logging.info("用户中断程序")
    except Exception as e:
        logging.error(f"程序执行异常: {e}")
        if notifier:
            notifier.send_error_notification(f"程序执行异常: {str(e)}")
    finally:
        logging.info("程序结束")


if __name__ == '__main__':
    main()

