---
description: 
globs: 
alwaysApply: false
---
# 架构说明

## 整体架构

项目采用模块化设计，主要分为以下几个层次：

1. 核心层（mycore）
   - 提供基础功能和服务
   - 定义核心接口和数据结构

2. Web 服务层（web1, web2）
   - 处理 HTTP 请求
   - 实现业务逻辑
   - 提供 API 接口

3. 应用层（app）
   - 整合各个模块
   - 提供完整的应用功能

4. 数据层
   - entity 模块定义数据模型
   - migration 模块处理数据库迁移

## 模块依赖关系

```
app
├── mycore
├── web1
└── web2
    └── mycore
```

## 扩展性设计

- 使用宏（macros）实现代码复用和扩展
- 模块间通过清晰的接口进行通信
- 支持 Docker 容器化部署

## 配置管理

- 使用 Cargo.toml 管理项目依赖
- 通过 docker-compose.yaml 管理服务配置
- 环境变量配置在 http-client.env.json 中


