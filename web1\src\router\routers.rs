use crate::router::auth;
use crate::router::auth::session_layer;
use crate::service::hello_world::hello_world;
use crate::service::user::{login, register, wx_login};
use axum::extract::{Path, Request};
use axum::routing::{delete, get, post};
use axum::{middleware, Form, Json, Router};
use mycore::config::state::AppState;
use serde::Deserialize;
use std::collections::HashMap;
use std::iter::Map;
use std::sync::Arc;
use tower_http::trace;
use tower_http::trace::{MakeSpan, TraceLayer};
use tracing::{info, Level};

#[derive(Deserialize, Debug)]
struct SignUp {
    username: String,
    password: String,
}

pub async fn init_route(state: Arc<AppState>) -> Router {
    let trace_layer = TraceLayer::new_for_http()
        //.on_request(trace::DefaultOnRequest::new().level(Level::INFO))
        .on_response(trace::DefaultOnResponse::new().level(Level::INFO))
        .on_failure(trace::DefaultOnFailure::new().level(Level::INFO))
        .make_span_with(
            trace::DefaultMakeSpan::new()
                .level(Level::INFO)
                .include_headers(false)
        );

    let session_layer = session_layer(state.clone()).await.unwrap();
    Router::new()
        .nest(
            "/my",
            Router::new()
                .route("/{id}/{id2}", get(|Path((id, id2)): Path<(i32, i32)>| async move {
                    format!("{}", id2)
                }))
                .route("/h/{id}", get(hello_world))
                .route("/post", post(|Form(name): Form<HashMap<String, String>>| async move {
                    info!("{:?}", name);
                    "dd"
                }))
                .route("/json", post(|Json(name): Json<HashMap<String, String>>| async move {
                    info!("{:?}", name);
                    "dd"
                }))
                .route("/json2", post(|req: Request| async move {
                    info!("{:?}", req.body());
                    "dd"
                }))
                .layer(middleware::from_fn_with_state(state.clone(), auth::auth))
            ,
        ).nest("/home2",
               Router::new()
                   .route("/{id}", get(|| async { "Hello, World!" }))
                   .route("/h/{id}", get(hello_world)),
    )
    .route("/register", post(register))
    .route("/login", post(login))
        .route("/wxlogin", post(wx_login))
        .layer(session_layer)
        .layer(trace_layer)
        .with_state(state)
}


