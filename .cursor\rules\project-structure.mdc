---
description: 
globs: 
alwaysApply: false
---
# 项目结构指南

这是一个基于 Rust 的工作流项目，采用工作空间（workspace）结构组织代码。

## 主要组件

- [Cargo.toml](mdc:Cargo.toml) - 项目主配置文件
- [src/](mdc:src) - 主程序源代码目录
- [mycore/](mdc:mycore) - 核心功能模块
- [web1/](mdc:web1) - Web 服务模块 1
- [web2/](mdc:web2) - Web 服务模块 2
- [app/](mdc:app) - 应用程序模块
- [migration/](mdc:migration) - 数据库迁移脚本
- [entity/](mdc:entity) - 数据实体定义
- [macros/](mdc:macros) - 自定义宏定义
- [wechat/](mdc:wechat) - 微信相关功能

## 配置文件

- [docker-compose.yaml](mdc:docker-compose.yaml) - Docker 编排配置
- [Dockerfile](mdc:Dockerfile) - Docker 构建文件
- [http-client.env.json](mdc:http-client.env.json) - HTTP 客户端环境配置


