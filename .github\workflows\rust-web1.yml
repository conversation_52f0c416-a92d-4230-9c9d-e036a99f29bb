name: Rust

on:
  push:
    branches: [ "main" ]
  pull_request:
    branches: [ "main" ]

env:
  CARGO_TERM_COLOR: always
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}
  BUILD: debug
jobs:
  build:

    runs-on: ${{ matrix.os }}
    permissions:
      contents: read
      packages: write
      id-token: write
      attestations: write
    strategy:
      matrix:
        # build: [linux, macos, windows]
        build: [linux]
        include:
          - build: linux
            os: ubuntu-latest
            rust: nightly
            target: x86_64-unknown-linux-gnu
            archive-name: web1-linux.tar.gz
          # - build: macos
          #   os: macos-latest
          #   rust: nightly
          #   target: x86_64-apple-darwin
          #   archive-name: web1-macos.tar.gz
          # - build: windows
          #   os: windows-2019
          #   rust: nightly-x86_64-msvc
          #   target: x86_64-pc-windows-msvc
          #   archive-name: web1-windows.7z
      fail-fast: false
    steps:
    - uses: actions/checkout@v4
    - name: Cache rust deb
      id: cache-rust-deb
      uses: actions/cache@v4
      with:
        path: |
            target
            ~/.cargo 
        key: ${{ matrix.os }}-cargo-${{ hashFiles('**/Cargo.lock') }}
    - name: Install
      run: rustup target add ${{ matrix.target }}
    - name: Build release
      if: env.BUILD == 'release'
      run: |
        cargo build --release --bin web1 --verbose --target ${{ matrix.target }}
        cargo build --release --bin web2 --verbose --target ${{ matrix.target }}
      env:
        RUST_BACKTRACE: 1
    - name: Build debug
      if: env.BUILD == 'debug'
      run: | 
        cargo build --bin web1 --verbose --target ${{ matrix.target }}
        cargo build --bin web2 --verbose --target ${{ matrix.target }}
    - name: Strip binary (linux and macos)
      if: matrix.build == 'linux' || matrix.build == 'macos'
      run: |
        strip "target/${{ matrix.target }}/${{ env.BUILD }}/web1"
        strip "target/${{ matrix.target }}/${{ env.BUILD }}/web2"
    - name: Build archive
      shell: bash
      run: |
           mkdir archive
           cd archive
           cp "../target/${{ matrix.target }}/${{ env.BUILD }}/web1" ./
           cp "../target/${{ matrix.target }}/${{ env.BUILD }}/web2" ./
           tar -czf "${{ matrix.archive-name }}" web1
    # - name: Build archive
    #   shell: bash
    #   run: |
    #       mkdir archive
    #       cd archive
    #       if [ "${{ matrix.build }}" = "windows" ]; then
    #         cp "../target/${{ matrix.target }}/release/web1.exe" ./
    #         7z a "${{ matrix.archive-name }}" web1.exe
    #       else
    #         cp "../target/${{ matrix.target }}/release/web1" ./
    #         tar -czf "${{ matrix.archive-name }}" web1
    #       fi
    - name: Upload archive
      uses: actions/upload-artifact@v4
      with:
        name: ${{ matrix.archive-name }}
        path: archive/${{ matrix.archive-name }}
    - name: Log in to the Container registry
      uses: docker/login-action@65b78e6e13532edd9afa3aa52ac7964289d1a9c1
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
    - name: Extract metadata (tags, labels) for Docker
      id: meta
      uses: docker/metadata-action@9ec57ed1fcdbf14dcef7dfbe97b2010124a938b7
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
    - name: Build and push Docker image
      id: push
      uses: docker/build-push-action@f2a1d5e99d037542a71f64918e516c093c6f3fc4
      with:
        context: .
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
          #    - name: Generate artifact attestation
          #      uses: actions/attest-build-provenance@v1
          #      with:
          #        subject-name: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME}}
          #        subject-digest: ${{ steps.push.outputs.digest }}
          #        push-to-registry: true          
