mod main_test;

use log::info;
use mycore::config::log::init_log;
use mycore::config::rt::{RT3, RT4, RT5};
use std::collections::HashMap;
use std::sync::{Arc, LazyLock, Mutex};
use sysinfo::{Pid, ProcessesToUpdate, System};


static SYSTEM: LazyLock<Arc<Mutex<System>>> = LazyLock::new(|| {
    let system = System::new_all();
    Arc::new(Mutex::new(system))
});

static PID: LazyLock<Pid> = LazyLock::new(|| Pid::from_u32(std::process::id()));

fn main() {
    init_log();
    print_mem();
    let db = duckdb::Connection::open("test.db2").unwrap();
    if let Ok(rt) = RT3.read() {
        rt.block_on(async {
            let mut tasks = vec![];
            for _i in 0..5 {
                tasks.push(rt.spawn(async {
                    // 优化：预分配 HashMap 容量，避免频繁重新分配
                    let mut v = HashMap::with_capacity(5000000);
                    for i in 0..5000000 {
                        let uid = "dssadfsdfsadfadsfsavsavfavvffdafasddf".to_string();
                        v.insert(i, uid);
                    }
                    print_mem();
                    v
                }));
            }
            for task in tasks {
                let _ = task.await;
            }
        });
    }
    //RT5 shutdown
    let b = RT5.read();
    b.shutdown_background();
    // 清理并优化运行时管理部分
    if let Ok(rt) = RT3.write() {
        let handler = rt.handle();
        let alive_tasks = rt.metrics().num_alive_tasks();
        info!("RT4 活跃任务数: {:?}", alive_tasks);
        print_mem();
    }
}

fn print_mem() {
    // 添加错误处理机制
    match SYSTEM.lock() {
        Ok(mut sys) => {
            sys.refresh_memory();
            sys.refresh_processes(ProcessesToUpdate::Some(&[*PID]), true);
            
            match sys.process(*PID) {
                Some(process) => {
                    let vm = process.virtual_memory() / 1024;
                    let m = process.memory() / 1024;
                    
                    // 添加运行时任务数量监控
                    match RT3.read() {
                        Ok(rt) => {
                            info!("活跃任务数: {:?}", rt.metrics().num_alive_tasks());
                        }
                        Err(e) => {
                            log::warn!("无法获取运行时指标: {:?}", e);
                        }
                    }
                    
                    info!("内存使用量: {} KB", m);
                    info!("虚拟内存: {} KB", vm);
                }
                None => {
                    log::warn!("无法找到当前进程信息 (PID: {:?})", *PID);
                }
            }
        }
        Err(e) => {
            log::error!("无法获取系统锁: {:?}", e);
        }
    }
}
