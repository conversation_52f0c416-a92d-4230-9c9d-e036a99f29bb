# NGA 论坛数据爬虫 - Rust 版本

这是 NGA 论坛数据爬虫的 Rust 实现版本，提供高性能、内存安全的数据抓取功能。

## 功能特性

- ✅ **高性能**：使用 Rust 异步编程，提供更好的性能表现
- ✅ **内存安全**：利用 Rust 的所有权系统，避免内存泄漏和数据竞争
- ✅ **数据抓取**：支持主题、帖子、回复数据抓取
- ✅ **Bark 推送**：实时推送新回复通知到 iOS 设备
- ✅ **配置管理**：通过环境变量管理所有配置
- ✅ **错误处理**：完善的异常处理和日志记录
- ✅ **数据库支持**：PostgreSQL 数据存储
- ✅ **命令行界面**：友好的命令行参数支持

## 快速开始

### 1. 安装 Rust

如果你还没有安装 Rust，请访问 [https://rustup.rs/](https://rustup.rs/) 安装 Rust 工具链。

### 2. 克隆项目

```bash
cd nga-rust
```

### 3. 配置环境变量

复制 `env.example` 为 `.env` 并填写配置：

```bash
cp env.example .env
# 编辑 .env 文件，填写你的配置
```

### 4. 编译项目

```bash
cargo build --release
```

### 5. 运行爬虫

```bash
# 爬取回复数据（默认模式）
cargo run --release

# 爬取主题数据
cargo run --release -- --mode subject --pages 100

# 爬取指定主题的帖子
cargo run --release -- --mode post --tid 12345

# 启用详细日志
cargo run --release -- --verbose
```

## 命令行参数

- `-m, --mode <MODE>`：运行模式 [reply, subject, post]，默认为 reply
- `-t, --tid <TID>`：主题ID（仅在 post 模式下使用）
- `-p, --pages <PAGES>`：最大页数（仅在 subject 模式下使用），默认为 5000
- `-f, --fid <FID>`：版块ID，默认为 706
- `-v, --verbose`：启用详细日志
- `-h, --help`：显示帮助信息

## 配置说明

### 数据库配置
- `DATABASE_URL`: PostgreSQL 连接字符串

### NGA 配置
- `NGA_COOKIE`: NGA 论坛 Cookie（必需）

### Bark 推送配置
- `BARK_ENABLED`: 是否启用 Bark 推送（true/false）
- `BARK_SERVER_URL`: Bark 服务器地址
- `BARK_DEVICE_KEY`: Bark 设备密钥
- `BARK_DEFAULT_PRIORITY`: 默认推送优先级（0-10）

### 日志配置
- `LOG_LEVEL`: 日志级别（DEBUG/INFO/WARNING/ERROR）
- `LOG_FILE`: 日志文件路径
- `RUST_LOG`: Rust 特定的日志配置

## 项目结构

```
nga-rust/
├── src/
│   ├── main.rs          # 程序入口
│   ├── config.rs        # 配置管理
│   ├── crawler.rs       # 爬虫核心逻辑
│   ├── models.rs        # 数据模型
│   └── notifier.rs      # Bark 推送模块
├── Cargo.toml           # 项目配置和依赖
├── env.example          # 配置示例
└── README.md           # 项目说明
```

## 性能优势

相比 Python 版本，Rust 版本具有以下优势：

1. **更高的性能**：Rust 的零成本抽象和编译优化
2. **更低的内存占用**：无垃圾收集器，手动内存管理
3. **更好的并发**：无 GIL 限制，真正的并行处理
4. **类型安全**：编译时错误检查，减少运行时错误

## 开发指南

### 添加新功能

1. 在相应的模块中添加功能代码
2. 更新相关的数据模型
3. 编写测试代码
4. 更新文档

### 运行测试

```bash
cargo test
```

### 代码格式化

```bash
cargo fmt
```

### 代码检查

```bash
cargo clippy
```

## 注意事项

- 请遵守 NGA 论坛的使用条款
- 建议设置合理的请求间隔，避免过于频繁的访问
- 妥善保管 Cookie 等敏感信息
- 定期更新 Cookie 以确保正常访问

## 更新日志

### v0.1.0
- 初始 Rust 版本发布
- 支持基础的数据抓取功能
- 实现 Bark 推送通知
- 完整的配置管理系统
- 异步编程支持
- 命令行界面

## 许可证

本项目遵循原 Python 版本的许可证条款。 