use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct NgaAuthor {
    pub id: Option<i32>,
    pub username: Option<String>,
    pub uid: Option<i32>,
    pub time_delay_sec: Option<i32>,
    pub last_reply_time: Option<DateTime<Utc>>,
    pub sub: Option<i32>,
}

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct NgaSubject {
    pub id: Option<i32>,
    pub tid: Option<i32>,
    pub fid: Option<i32>,
    pub author: Option<String>,
    pub authorid: Option<i32>,
    pub subject: Option<String>,
    pub postdate: Option<DateTime<Utc>>,
    pub lastpost: Option<DateTime<Utc>>,
    pub lastposter: Option<String>,
    pub replies: Option<i32>,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct NgaPost {
    pub tid: Option<i32>,
    pub pid: Option<i32>,
    pub author: Option<String>,
    pub authorid: Option<i32>,
    pub postdate: Option<DateTime<Utc>>,
    pub content: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NgaApiResponse<T> {
    pub code: Option<i32>,
    pub data: Option<T>,
    pub message: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SubjectListData {
    pub __F: Option<serde_json::Value>,
    pub __R: Option<serde_json::Value>,
    pub __ROWS: Option<serde_json::Value>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PostListData {
    pub __F: Option<serde_json::Value>,
    pub __R: Option<serde_json::Value>,
    pub __ROWS: Option<serde_json::Value>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserReplyData {
    pub __F: Option<serde_json::Value>,
    pub __R: Option<serde_json::Value>,
    pub __ROWS: Option<serde_json::Value>,
}

impl NgaAuthor {
    pub async fn create_table(pool: &sqlx::PgPool) -> sqlx::Result<()> {
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS nga_author (
                id SERIAL PRIMARY KEY,
                username VARCHAR(200),
                uid INTEGER,
                time_delay_sec INTEGER,
                last_reply_time TIMESTAMP,
                sub INTEGER
            )
            "#,
        )
        .execute(pool)
        .await?;
        Ok(())
    }

    pub async fn insert_or_update(&self, pool: &sqlx::PgPool) -> sqlx::Result<()> {
        sqlx::query(
            r#"
            INSERT INTO nga_author (username, uid, time_delay_sec, last_reply_time, sub)
            VALUES ($1, $2, $3, $4, $5)
            ON CONFLICT (uid) DO UPDATE SET
                username = EXCLUDED.username,
                time_delay_sec = EXCLUDED.time_delay_sec,
                last_reply_time = EXCLUDED.last_reply_time,
                sub = EXCLUDED.sub
            "#,
        )
        .bind(&self.username)
        .bind(&self.uid)
        .bind(&self.time_delay_sec)
        .bind(&self.last_reply_time)
        .bind(&self.sub)
        .execute(pool)
        .await?;
        Ok(())
    }
}

impl NgaSubject {
    pub async fn create_table(pool: &sqlx::PgPool) -> sqlx::Result<()> {
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS nga_subjects (
                id SERIAL PRIMARY KEY,
                tid INTEGER UNIQUE,
                fid INTEGER,
                author VARCHAR(200),
                authorid INTEGER,
                subject VARCHAR(200),
                postdate TIMESTAMP,
                lastpost TIMESTAMP,
                lastposter VARCHAR(200),
                replies INTEGER,
                UNIQUE(tid, fid)
            )
            "#,
        )
        .execute(pool)
        .await?;
        Ok(())
    }

    pub async fn insert_or_update(&self, pool: &sqlx::PgPool) -> sqlx::Result<()> {
        sqlx::query(
            r#"
            INSERT INTO nga_subjects (tid, fid, author, authorid, subject, postdate, lastpost, lastposter, replies)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
            ON CONFLICT (tid, fid) DO UPDATE SET
                author = EXCLUDED.author,
                authorid = EXCLUDED.authorid,
                subject = EXCLUDED.subject,
                postdate = EXCLUDED.postdate,
                lastpost = EXCLUDED.lastpost,
                lastposter = EXCLUDED.lastposter,
                replies = EXCLUDED.replies
            "#,
        )
        .bind(&self.tid)
        .bind(&self.fid)
        .bind(&self.author)
        .bind(&self.authorid)
        .bind(&self.subject)
        .bind(&self.postdate)
        .bind(&self.lastpost)
        .bind(&self.lastposter)
        .bind(&self.replies)
        .execute(pool)
        .await?;
        Ok(())
    }
} 