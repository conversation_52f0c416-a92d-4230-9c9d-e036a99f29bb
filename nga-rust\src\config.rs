use std::collections::HashMap;
use serde::{Deserialize, Serialize};
use anyhow::Result;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct DatabaseConfig {
    pub url: String,
    pub echo: bool,
}

impl Default for DatabaseConfig {
    fn default() -> Self {
        Self {
            url: std::env::var("DATABASE_URL")
                .unwrap_or_else(|_| "postgresql://nga:5mXxCEFwfPidAftf@localhost:5432/nga".to_string()),
            echo: false,
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NgaConfig {
    pub prefix_url: String,
    pub headers: HashMap<String, String>,
    pub cookie: String,
}

impl Default for NgaConfig {
    fn default() -> Self {
        let mut headers = HashMap::new();
        headers.insert("User-Agent".to_string(), "NGA_skull/6.0.7(iPhone11,6;iOS 12.2)".to_string());
        headers.insert("X-User-Agent".to_string(), "NGA_skull/6.0.7(iPhone11,6;iOS 12.2)".to_string());
        headers.insert("Content-Type".to_string(), "application/x-www-form-urlencoded".to_string());

        Self {
            prefix_url: "http://ngabbs.com/app_api.php?".to_string(),
            headers,
            cookie: std::env::var("NGA_COOKIE").unwrap_or_default(),
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BarkConfig {
    pub enabled: bool,
    pub server_url: String,
    pub device_key: String,
    pub default_priority: i32,
}

impl Default for BarkConfig {
    fn default() -> Self {
        Self {
            enabled: std::env::var("BARK_ENABLED")
                .unwrap_or_else(|_| "false".to_string())
                .to_lowercase() == "true",
            server_url: std::env::var("BARK_SERVER_URL").unwrap_or_default(),
            device_key: std::env::var("BARK_DEVICE_KEY").unwrap_or_default(),
            default_priority: std::env::var("BARK_DEFAULT_PRIORITY")
                .unwrap_or_else(|_| "0".to_string())
                .parse()
                .unwrap_or(0),
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LogConfig {
    pub level: String,
    pub file_path: String,
    pub format: String,
    pub date_format: String,
}

impl Default for LogConfig {
    fn default() -> Self {
        Self {
            level: std::env::var("LOG_LEVEL").unwrap_or_else(|_| "INFO".to_string()),
            file_path: std::env::var("LOG_FILE").unwrap_or_else(|_| "/home/<USER>/nga.log".to_string()),
            format: "%(asctime)s.%(msecs)03d [%(levelname)s] [%(filename)s:%(lineno)d] %(message)s".to_string(),
            date_format: "## %Y-%m-%d %H:%M:%S".to_string(),
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AppConfig {
    pub database: DatabaseConfig,
    pub nga: NgaConfig,
    pub bark: BarkConfig,
    pub log: LogConfig,
}

impl Default for AppConfig {
    fn default() -> Self {
        Self {
            database: DatabaseConfig::default(),
            nga: NgaConfig::default(),
            bark: BarkConfig::default(),
            log: LogConfig::default(),
        }
    }
}

impl AppConfig {
    pub fn load() -> Result<Self> {
        dotenvy::dotenv().ok();
        Ok(Self::default())
    }
} 