[package]
name = "mycore"
version = "0.1.0"
edition = "2021"

[dependencies]
tokio={version='*', features=["rt-multi-thread", "tracing", "macros"]}
dotenv = "*"
sea-orm = { version = "1.1.11", features = ["sqlx-sqlite", "sqlx-mysql", "sqlx-postgres", "runtime-tokio-rustls"] }
tracing= { version = '*' }
tracing-subscriber= { version = '*', features = ["chrono"] }
log = "0.4.22"
redis = { version = "0.29.5", features = ["r2d2", "tokio-rustls-comp"] }
redis_pool = "0.8.0"
