package models

import (
	"time"

	"gorm.io/gorm"
)

// NGAAuthor NGA 作者模型
type NGAAuthor struct {
	ID            uint           `json:"id" gorm:"primaryKey;comment:主键ID"`
	Username      string         `json:"username" gorm:"size:200;comment:用户名"`
	UID           int            `json:"uid" gorm:"comment:用户ID"`
	TimeDelaySec  int            `json:"time_delay_sec" gorm:"comment:用户发帖延时"`
	LastReplyTime *time.Time     `json:"last_reply_time" gorm:"comment:上次回复时间"`
	Sub           int            `json:"sub" gorm:"comment:是否订阅"`
	CreatedAt     time.Time      `json:"created_at"`
	UpdatedAt     time.Time      `json:"updated_at"`
	DeletedAt     gorm.DeletedAt `json:"deleted_at" gorm:"index"`
}

// TableName 指定表名
func (NGAAuthor) TableName() string {
	return "nga_author"
}

// NGASubject NGA 主题模型
type NGASubject struct {
	ID         uint           `json:"id" gorm:"primaryKey;comment:主键ID"`
	TID        int            `json:"tid" gorm:"uniqueIndex;comment:主题ID"`
	FID        int            `json:"fid" gorm:"comment:版块ID"`
	Author     string         `json:"author" gorm:"size:200;comment:作者"`
	AuthorID   int            `json:"author_id" gorm:"comment:作者ID"`
	Subject    string         `json:"subject" gorm:"size:200;comment:主题"`
	PostDate   time.Time      `json:"post_date" gorm:"comment:发表时间"`
	LastPost   time.Time      `json:"last_post" gorm:"comment:最后发表时间"`
	LastPoster string         `json:"last_poster" gorm:"size:200;comment:最后发表作者"`
	Replies    int            `json:"replies" gorm:"comment:回复数"`
	CreatedAt  time.Time      `json:"created_at"`
	UpdatedAt  time.Time      `json:"updated_at"`
	DeletedAt  gorm.DeletedAt `json:"deleted_at" gorm:"index"`
}

// TableName 指定表名
func (NGASubject) TableName() string {
	return "nga_subjects"
}

// NGAPost NGA 帖子模型
type NGAPost struct {
	ID        uint           `json:"id" gorm:"primaryKey;comment:主键ID"`
	TID       int            `json:"tid" gorm:"index;comment:主题ID"`
	PID       int            `json:"pid" gorm:"uniqueIndex;comment:帖子ID"`
	Author    string         `json:"author" gorm:"size:200;comment:作者"`
	AuthorID  int            `json:"author_id" gorm:"comment:作者ID"`
	Content   string         `json:"content" gorm:"type:text;comment:内容"`
	PostDate  time.Time      `json:"post_date" gorm:"comment:发表时间"`
	Floor     int            `json:"floor" gorm:"comment:楼层"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"deleted_at" gorm:"index"`
}

// TableName 指定表名
func (NGAPost) TableName() string {
	return "nga_posts"
}

// 添加索引约束
func init() {
	// 可以在这里添加额外的索引定义
}
