use crate::config::rt::RT;
use crate::config::state::AppState;
use log::info;
use redis::Client;
use redis_pool::SingleRedisPool;
// use duckdb::Connection;
use sea_orm::{ConnectOptions, Database};
use std::env;
use std::fs::File;
use std::sync::{Arc, OnceLock};
use std::time::Duration;
use tracing::log;

static STATE: OnceLock<Arc<AppState>> = OnceLock::new();

pub fn init_db() -> Arc<AppState> {
    STATE.get_or_init(|| {
        info!("start init db");
        if !std::path::Path::new("test.db").exists() {
            let _ = File::create("test.db");
        }
        dotenv::dotenv().ok();
        let db_url = env::var("DATABASE_URL").expect("");
        let redis_url = env::var("REDIS_URL").expect("");
        let redis = SingleRedisPool::new(Client::open(redis_url).unwrap(), 10, Some(10));

        // let db_url2 = env!("DATABASE_URL");

        // let duckdb = Connection::open("duck.db").unwrap();
        let mut opt = ConnectOptions::new(db_url);
        opt.max_connections(10)
            .min_connections(2)
            .connect_timeout(std::time::Duration::from_secs(8))
            .idle_timeout(std::time::Duration::from_secs(8 * 60))
            .max_lifetime(std::time::Duration::from_secs(9 * 60))
            .sqlx_logging(true)
            .sqlx_logging_level(log::LevelFilter::Debug)
            .sqlx_slow_statements_logging_settings(log::LevelFilter::Info, Duration::from_millis(100));
        let db = RT.block_on(async {Database::connect(opt).await.unwrap()});
        Arc::new(AppState { db, redis})
    }).clone()
}

