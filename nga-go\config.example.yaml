# NGA 爬虫配置文件示例 (Go 版本)
# 复制此文件为 config.yaml 并填写实际配置值

database:
  url: "postgresql://nga:password@localhost:5432/nga?sslmode=disable"
  echo: false

nga:
  prefix_url: "http://ngabbs.com/app_api.php?"
  cookie: "your_nga_cookie_here"
  headers:
    User-Agent: "NGA_skull/6.0.7(iPhone11,6;iOS 12.2)"
    X-User-Agent: "NGA_skull/6.0.7(iPhone11,6;iOS 12.2)"
    Content-Type: "application/x-www-form-urlencoded"

bark:
  enabled: true
  server_url: "https://api.day.app"
  device_key: "your_device_key_here"
  default_priority: 0

log:
  level: "info"
  file_path: "./logs/nga.log"
  format: "json" 