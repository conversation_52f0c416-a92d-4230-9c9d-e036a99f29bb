use std::sync::{<PERSON>, LazyLock, RwLock};
use tokio::runtime::{Builder, Runtime};
use tokio::sync::Mutex;

pub static RT: LazyLock<Arc<Runtime>> =
    LazyLock::new(|| Arc::new(Builder::new_multi_thread()
        .enable_all()
        .thread_name("mycore")
        .worker_threads(10)
        .build()
        .unwrap()));

pub static RT2: LazyLock<Arc<Runtime>> =
    LazyLock::new(|| Arc::new(Builder::new_multi_thread()
        .enable_all()
        .thread_name("mycore2")
        .worker_threads(3)
        .build()
        .unwrap()));

pub static RT3: LazyLock<RwLock<Runtime>> =
    LazyLock::new(|| RwLock::new(Builder::new_multi_thread()
        .enable_all()
        .thread_name("mycore3")
        .worker_threads(3)
        .build()
        .unwrap()));

pub static RT4: LazyLock<RwLock<Runtime>> =
    LazyLock::new(|| RwLock::new(Builder::new_multi_thread()
        .enable_all()
        .thread_name("mycore4")
        .worker_threads(3)
        .build()
        .unwrap()));



pub static RT5: LazyLock<Mutex<Runtime>> =
    LazyLock::new(|| Mutex::new(Builder::new_multi_thread()
        .enable_all()
        .thread_name("mycore3")
        .worker_threads(3)
        .build()
        .unwrap()));

