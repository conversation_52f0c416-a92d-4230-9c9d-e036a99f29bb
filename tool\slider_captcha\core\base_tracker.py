"""
轨迹生成器抽象基类

定义了滑块验证码轨迹生成的标准接口，所有具体的轨迹生成器实现都应该继承此基类。
"""

from abc import ABC, abstractmethod
from typing import List, Tuple, Dict, Any, Optional
import logging


class BaseTracker(ABC):
    """滑块验证码轨迹生成器抽象基类"""
    
    def __init__(self, config_manager=None):
        """
        初始化轨迹生成器
        
        Args:
            config_manager: 配置管理器实例
        """
        self.config_manager = config_manager
        self.logger = logging.getLogger(self.__class__.__name__)
        self._generation_count = 0
        self._last_trajectory_length = 0
        
    @abstractmethod
    def generate_trajectory(self, distance: int, 
                          duration: float = 2.0,
                          **kwargs) -> List[Tuple[int, float]]:
        """
        生成滑块拖动轨迹
        
        Args:
            distance: 需要滑动的总距离（像素）
            duration: 滑动持续时间（秒）
            **kwargs: 其他轨迹生成参数
            
        Returns:
            List[Tuple[int, float]]: 轨迹点列表，每个元素为(位移, 时间间隔)
            - 位移：相对于前一个位置的像素偏移量
            - 时间间隔：距离前一个操作的时间间隔（秒）
            
        Raises:
            ValueError: 当输入参数无效时抛出
        """
        pass
    
    @abstractmethod
    def set_trajectory_params(self, params: Dict[str, Any]) -> None:
        """
        设置轨迹生成参数
        
        Args:
            params: 轨迹生成参数字典
        """
        pass
    
    @abstractmethod
    def get_trajectory_info(self) -> Dict[str, Any]:
        """
        获取轨迹生成器的信息和配置
        
        Returns:
            Dict[str, Any]: 包含轨迹生成器类型、参数等信息
        """
        pass
    
    def validate_trajectory_params(self, distance: int, duration: float) -> None:
        """
        验证轨迹生成参数的有效性
        
        Args:
            distance: 滑动距离
            duration: 持续时间
            
        Raises:
            ValueError: 当参数无效时抛出
        """
        if not isinstance(distance, (int, float)) or distance <= 0:
            raise ValueError(f"滑动距离必须是正数，当前值: {distance}")
            
        if not isinstance(duration, (int, float)) or duration <= 0:
            raise ValueError(f"持续时间必须是正数，当前值: {duration}")
            
        if distance > 1000:
            raise ValueError(f"滑动距离过大，当前值: {distance}，最大允许: 1000")
            
        if duration > 10:
            raise ValueError(f"持续时间过长，当前值: {duration}，最大允许: 10秒")
    
    def smooth_trajectory(self, trajectory: List[Tuple[int, float]], 
                         smoothing_factor: float = 0.1) -> List[Tuple[int, float]]:
        """
        对轨迹进行平滑处理
        
        Args:
            trajectory: 原始轨迹
            smoothing_factor: 平滑因子，范围[0, 1]
            
        Returns:
            List[Tuple[int, float]]: 平滑后的轨迹
        """
        if not trajectory or len(trajectory) <= 2:
            return trajectory
            
        smoothed = [trajectory[0]]  # 保持起始点不变
        
        for i in range(1, len(trajectory) - 1):
            prev_displacement, prev_time = trajectory[i-1]
            curr_displacement, curr_time = trajectory[i]
            next_displacement, next_time = trajectory[i+1]
            
            # 简单的移动平均平滑
            smoothed_displacement = int(
                curr_displacement * (1 - smoothing_factor) +
                (prev_displacement + next_displacement) / 2 * smoothing_factor
            )
            
            smoothed.append((smoothed_displacement, curr_time))
        
        smoothed.append(trajectory[-1])  # 保持结束点不变
        return smoothed
    
    def add_random_jitter(self, trajectory: List[Tuple[int, float]], 
                         jitter_strength: float = 0.1) -> List[Tuple[int, float]]:
        """
        为轨迹添加随机抖动，使其更加真实
        
        Args:
            trajectory: 原始轨迹
            jitter_strength: 抖动强度，范围[0, 1]
            
        Returns:
            List[Tuple[int, float]]: 添加抖动后的轨迹
        """
        import random
        
        jittered = []
        for displacement, time_interval in trajectory:
            # 添加随机位移抖动（小幅度）
            jitter = int(random.uniform(-2, 2) * jitter_strength)
            jittered_displacement = displacement + jitter
            
            # 添加随机时间抖动（小幅度）
            time_jitter = random.uniform(-0.02, 0.02) * jitter_strength
            jittered_time = max(0.01, time_interval + time_jitter)  # 确保时间间隔为正
            
            jittered.append((jittered_displacement, jittered_time))
        
        return jittered
    
    def get_generation_stats(self) -> Dict[str, Any]:
        """
        获取轨迹生成统计信息
        
        Returns:
            Dict[str, Any]: 包含生成次数、最后轨迹长度等统计信息
        """
        return {
            'generation_count': self._generation_count,
            'last_trajectory_length': self._last_trajectory_length,
            'tracker_type': self.__class__.__name__
        }
    
    def _update_stats(self, trajectory_length: int) -> None:
        """
        更新轨迹生成统计信息
        
        Args:
            trajectory_length: 本次生成的轨迹长度
        """
        self._generation_count += 1
        self._last_trajectory_length = trajectory_length
        
        self.logger.debug(f"轨迹生成完成 - 轨迹长度: {trajectory_length}, 总生成次数: {self._generation_count}")
    
    def reset_stats(self) -> None:
        """重置轨迹生成统计信息"""
        self._generation_count = 0
        self._last_trajectory_length = 0
        self.logger.debug("轨迹生成统计信息已重置")
    
    def visualize_trajectory(self, trajectory: List[Tuple[int, float]], 
                           save_path: Optional[str] = None) -> None:
        """
        可视化轨迹（可选功能）
        
        Args:
            trajectory: 要可视化的轨迹
            save_path: 保存路径，如果为None则显示图像
        """
        try:
            import matplotlib.pyplot as plt
            
            # 计算累积位移
            positions = [0]
            times = [0]
            
            for displacement, time_interval in trajectory:
                positions.append(positions[-1] + displacement)
                times.append(times[-1] + time_interval)
            
            plt.figure(figsize=(12, 4))
            
            # 位移图
            plt.subplot(1, 2, 1)
            plt.plot(times, positions, 'b-', linewidth=2)
            plt.xlabel('时间 (秒)')
            plt.ylabel('位置 (像素)')
            plt.title('滑块轨迹 - 位置随时间变化')
            plt.grid(True, alpha=0.3)
            
            # 速度图
            plt.subplot(1, 2, 2)
            velocities = []
            for i in range(1, len(positions)):
                velocity = (positions[i] - positions[i-1]) / (times[i] - times[i-1])
                velocities.append(velocity)
            
            plt.plot(times[1:], velocities, 'r-', linewidth=2)
            plt.xlabel('时间 (秒)')
            plt.ylabel('速度 (像素/秒)')
            plt.title('滑块轨迹 - 速度随时间变化')
            plt.grid(True, alpha=0.3)
            
            plt.tight_layout()
            
            if save_path:
                plt.savefig(save_path, dpi=150, bbox_inches='tight')
                self.logger.info(f"轨迹可视化已保存到: {save_path}")
            else:
                plt.show()
                
        except ImportError:
            self.logger.warning("无法导入matplotlib，跳过轨迹可视化")
    
    def __str__(self) -> str:
        """返回轨迹生成器的字符串表示"""
        return f"{self.__class__.__name__}(生成次数={self._generation_count}, 最后轨迹长度={self._last_trajectory_length})"
    
    def __repr__(self) -> str:
        """返回轨迹生成器的详细字符串表示"""
        return (f"{self.__class__.__name__}("
                f"generation_count={self._generation_count}, "
                f"last_trajectory_length={self._last_trajectory_length})") 