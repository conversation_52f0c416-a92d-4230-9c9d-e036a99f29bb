# NGA 爬虫 Makefile
.PHONY: build run clean test fmt vet deps help

# Go 相关变量
GOCMD=go
GOBUILD=$(GOCMD) build
GOCLEAN=$(GOCMD) clean
GOTEST=$(GOCMD) test
GOGET=$(GOCMD) get
GOMOD=$(GOCMD) mod
GOFMT=gofmt

# 应用变量
BINARY_NAME=nga-go
BINARY_PATH=./bin/$(BINARY_NAME)
MAIN_PATH=./cmd/nga-go

# 默认目标
all: build

# 构建应用
build:
	@echo "正在构建 $(BINARY_NAME)..."
	@mkdir -p bin
	CGO_ENABLED=1 $(GOBUILD) -o $(BINARY_PATH) -v $(MAIN_PATH)
	@echo "构建完成: $(BINARY_PATH)"

# 构建 Windows 版本
build-windows:
	@echo "正在构建 Windows 版本..."
	@mkdir -p bin
	CGO_ENABLED=1 GOOS=windows GOARCH=amd64 $(GOBUILD) -o ./bin/$(BINARY_NAME).exe -v $(MAIN_PATH)

# 构建 Linux 版本
build-linux:
	@echo "正在构建 Linux 版本..."
	@mkdir -p bin
	CGO_ENABLED=1 GOOS=linux GOARCH=amd64 $(GOBUILD) -o ./bin/$(BINARY_NAME)-linux -v $(MAIN_PATH)

# 构建所有平台版本
build-all: build-windows build-linux build

# 运行应用 (回复监控模式)
run:
	$(GOBUILD) -o $(BINARY_PATH) $(MAIN_PATH) && $(BINARY_PATH) reply

# 运行应用 (主题抓取模式)
run-subject:
	$(GOBUILD) -o $(BINARY_PATH) $(MAIN_PATH) && $(BINARY_PATH) subject --max-pages 10

# 清理构建文件
clean:
	$(GOCLEAN)
	rm -rf bin/
	rm -rf logs/

# 运行测试
test:
	$(GOTEST) -v ./...

# 运行基准测试
bench:
	$(GOTEST) -bench=. -benchmem ./...

# 格式化代码
fmt:
	$(GOFMT) -w .

# 代码检查
vet:
	$(GOCMD) vet ./...

# 静态分析
lint:
	golangci-lint run

# 下载依赖
deps:
	$(GOMOD) download
	$(GOMOD) tidy

# 更新依赖
deps-update:
	$(GOGET) -u ./...
	$(GOMOD) tidy

# 创建日志目录
setup-dirs:
	@mkdir -p logs bin

# 初始化项目
init: deps setup-dirs
	@echo "项目初始化完成"

# 安装到系统
install:
	$(GOCMD) install $(MAIN_PATH)

# 显示帮助信息
help:
	@echo "可用命令："
	@echo "  build         - 构建应用程序"
	@echo "  build-windows - 构建 Windows 版本"
	@echo "  build-linux   - 构建 Linux 版本"
	@echo "  build-all     - 构建所有平台版本"
	@echo "  run           - 运行应用程序 (回复监控)"
	@echo "  run-subject   - 运行应用程序 (主题抓取)"
	@echo "  clean         - 清理构建文件"
	@echo "  test          - 运行测试"
	@echo "  bench         - 运行基准测试"
	@echo "  fmt           - 格式化代码"
	@echo "  vet           - 代码检查"
	@echo "  lint          - 静态分析"
	@echo "  deps          - 下载依赖"
	@echo "  deps-update   - 更新依赖"
	@echo "  setup-dirs    - 创建必要目录"
	@echo "  init          - 初始化项目"
	@echo "  install       - 安装到系统"
	@echo "  help          - 显示此帮助信息" 