"""
滑块验证码自动解决方案

这是一个基于OpenCV图像识别和Selenium浏览器自动化的滑块验证码破解工具。
支持多种验证码类型，采用模块化设计，易于扩展和维护。

主要特性：
- 支持主流滑块验证码（腾讯防水墙、极验、网易易盾等）
- 模块化设计，支持插件式扩展
- 基于物理模型的轨迹生成，提高成功率
- 集成反检测机制
- 配置文件驱动，易于适配新网站

使用示例：
    from slider_captcha import SliderCaptchaSolver
    
    solver = SliderCaptchaSolver()
    success = solver.solve_captcha(url="https://example.com/login")
"""

__version__ = "1.0.0"
__author__ = "AI Assistant"

from .core.slider_controller import SliderController
from .core.config_manager import ConfigManager
from .detectors.opencv_detector import OpenCVDetector
from .trackers.physics_tracker import PhysicsTracker
from .drivers.selenium_driver import SeleniumDriver

# 主要导出类
__all__ = [
    'SliderController',
    'ConfigManager', 
    'OpenCVDetector',
    'PhysicsTracker',
    'SeleniumDriver'
]


class SliderCaptchaSolver:
    """滑块验证码解决器的便捷接口"""
    
    def __init__(self, config_path: str = None):
        """
        初始化滑块验证码解决器
        
        Args:
            config_path: 配置文件路径，默认使用内置配置
        """
        self.config_manager = ConfigManager(config_path)
        self.detector = OpenCVDetector(self.config_manager)
        self.tracker = PhysicsTracker(self.config_manager)
        self.driver = SeleniumDriver(self.config_manager)
        self.controller = SliderController(
            detector=self.detector,
            tracker=self.tracker,
            driver=self.driver,
            config_manager=self.config_manager
        )
    
    def solve_captcha(self, url: str = None, timeout: int = 30) -> bool:
        """
        解决滑块验证码
        
        Args:
            url: 目标网页URL，如果为None则使用当前页面
            timeout: 超时时间（秒）
            
        Returns:
            bool: 是否成功解决验证码
        """
        return self.controller.solve_captcha(url=url, timeout=timeout)
    
    def close(self):
        """关闭浏览器驱动"""
        if self.driver:
            self.driver.close() 