[package]
name = "web1"
version = "0.1.0"
edition = "2021"

[dependencies]
axum = "*"
tokio={version='*', features=["rt-multi-thread", "tracing"]}
mycore = {path="../mycore"}
entity = { version = "0.1.0", path = "../entity" }
serde="*"
tracing= { version = '*' }
tracing-subscriber= { version = '*', features = ["chrono"] }
axum_session="*"
axum_session_sqlx= { version = "0.5.0", features = ["mysql", "sqlite", "postgres"] }
axum_session_redispool = { version = "0.6.0" }
sea-orm = { version = "1.1.12", features = ["sqlx-sqlite", "sqlx-mysql", "sqlx-postgres", "runtime-tokio-rustls"] }
sqlx = { version = "0.8.5", features = ["mysql", "sqlite", "postgres", "runtime-tokio-native-tls"] }
anyhow = "*"
thiserror = "*"
log = { version = "0.4.22", features = [] }
validator = { version = "*", features = ["derive"] }
regex = "1.10.6"
uuid = { version = "1.10.0", features = ["v4"] }
bcrypt = "*"
jsonwebtoken = "9.3.1"
chrono = "0.4.41"
redis = { version = "0.29.5", features = ["json"] }
reqwest = "0.12.15"
wechat = { version = "0.2.0", path = "../../wechat-rs", features = ["weapp", "open"]}
serde_json = {version = "1.0.140"}
tower = { version = "*", features = ["full"] }
tower-http = {version = "0.6.4", features = ["full"]}
flamegraph = {version = "0.6.8"}
flame = {version = "0.2.2"}