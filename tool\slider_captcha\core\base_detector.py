"""
缺口检测器抽象基类

定义了滑块验证码缺口检测的标准接口，所有具体的检测器实现都应该继承此基类。
"""

from abc import ABC, abstractmethod
from typing import Tuple, Optional, Dict, Any
import numpy as np
import logging


class BaseDetector(ABC):
    """滑块验证码缺口检测器抽象基类"""
    
    def __init__(self, config_manager=None):
        """
        初始化检测器
        
        Args:
            config_manager: 配置管理器实例
        """
        self.config_manager = config_manager
        self.logger = logging.getLogger(self.__class__.__name__)
        self._last_confidence = 0.0
        self._detection_count = 0
        
    @abstractmethod
    def detect_gap(self, background_img: np.ndarray, 
                   slider_img: Optional[np.ndarray] = None,
                   **kwargs) -> Tuple[int, int]:
        """
        检测滑块验证码中的缺口位置
        
        Args:
            background_img: 背景图像的numpy数组 (H, W, C)
            slider_img: 滑块图像的numpy数组，可选 (H, W, C)
            **kwargs: 其他检测参数
            
        Returns:
            Tuple[int, int]: 缺口的(x, y)坐标位置
            
        Raises:
            ValueError: 当无法检测到有效缺口时抛出
        """
        pass
    
    @abstractmethod 
    def get_confidence(self) -> float:
        """
        获取最近一次检测的置信度
        
        Returns:
            float: 置信度值，范围[0.0, 1.0]，1.0表示最高置信度
        """
        pass
    
    @abstractmethod
    def set_detection_params(self, params: Dict[str, Any]) -> None:
        """
        设置检测参数
        
        Args:
            params: 检测参数字典
        """
        pass
    
    def get_detection_stats(self) -> Dict[str, Any]:
        """
        获取检测统计信息
        
        Returns:
            Dict[str, Any]: 包含检测次数、平均置信度等统计信息
        """
        return {
            'detection_count': self._detection_count,
            'last_confidence': self._last_confidence,
            'detector_type': self.__class__.__name__
        }
    
    def validate_image(self, img: np.ndarray, img_name: str = "image") -> None:
        """
        验证输入图像的有效性
        
        Args:
            img: 要验证的图像数组
            img_name: 图像名称，用于错误消息
            
        Raises:
            ValueError: 当图像无效时抛出
        """
        if img is None:
            raise ValueError(f"{img_name} 不能为空")
            
        if not isinstance(img, np.ndarray):
            raise ValueError(f"{img_name} 必须是numpy数组")
            
        if img.size == 0:
            raise ValueError(f"{img_name} 不能为空数组")
            
        if len(img.shape) not in [2, 3]:
            raise ValueError(f"{img_name} 必须是2D或3D数组")
            
        if len(img.shape) == 3 and img.shape[2] not in [1, 3, 4]:
            raise ValueError(f"{img_name} 的通道数必须是1、3或4")
    
    def _update_stats(self, confidence: float) -> None:
        """
        更新检测统计信息
        
        Args:
            confidence: 本次检测的置信度
        """
        self._last_confidence = confidence
        self._detection_count += 1
        
        self.logger.debug(f"检测完成 - 置信度: {confidence:.3f}, 总检测次数: {self._detection_count}")
    
    def reset_stats(self) -> None:
        """重置检测统计信息"""
        self._last_confidence = 0.0
        self._detection_count = 0
        self.logger.debug("检测统计信息已重置")
        
    def __str__(self) -> str:
        """返回检测器的字符串表示"""
        return f"{self.__class__.__name__}(检测次数={self._detection_count}, 最新置信度={self._last_confidence:.3f})"
    
    def __repr__(self) -> str:
        """返回检测器的详细字符串表示"""
        return (f"{self.__class__.__name__}("
                f"detection_count={self._detection_count}, "
                f"last_confidence={self._last_confidence:.3f})") 